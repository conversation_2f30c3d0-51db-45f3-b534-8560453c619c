<?php get_header(); ?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 text-center">
            <div class="error-page mb-5">
                <h1 class="display-1 fw-bold text-primary mb-4">404</h1>
                <h2 class="h4 mb-4">Oops! Page Not Found</h2>
                <p class="text-muted mb-4">The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.</p>
                
                <div class="d-flex justify-content-center gap-3 mb-5">
                    <a href="<?php echo home_url(); ?>" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>Back to Home
                    </a>
                    <a href="javascript:history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Go Back
                    </a>
                </div>

                <div class="suggestions">
                    <h3 class="h5 mb-4">You might be interested in:</h3>
                    <?php
                    $recent_novels = new WP_Query([
                        'post_type' => 'novel',
                        'posts_per_page' => 3,
                        'orderby' => 'modified',
                        'order' => 'DESC'
                    ]);

                    if ($recent_novels->have_posts()) : ?>
                        <div class="row row-cols-1 row-cols-md-3 g-4">
                            <?php while ($recent_novels->have_posts()) : $recent_novels->the_post(); ?>
                                <div class="col">
                                    <div class="card h-100 novel-card">
                                        <div class="novel-cover">
                                            <?php if (has_post_thumbnail()) : ?>
                                                <a href="<?php the_permalink(); ?>">
                                                    <?php the_post_thumbnail('novel-cover', ['class' => 'card-img-top']); ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                        <div class="card-body p-2">
                                            <h5 class="card-title">
                                                <a href="<?php the_permalink(); ?>" class="text-decoration-none text-truncate d-block">
                                                    <?php the_title(); ?>
                                                </a>
                                            </h5>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php
                    endif;
                    wp_reset_postdata();
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 3rem 0;
}

.error-page .display-1 {
    font-size: 6rem;
    text-shadow: 2px 2px 0 rgba(0,0,0,0.1);
}

.novel-card {
    transition: transform 0.2s;
    border: 1px solid rgba(0,0,0,.125);
    background: #fff;
}

.novel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.novel-cover {
    position: relative;
    padding: 8px;
    background: #f8f9fa;
}

.card-img-top {
    width: 100%;
    height: 280px;
    object-fit: contain;
    background: #f8f9fa;
}

.card-title {
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
    height: 2.8em;
    overflow: hidden;
}

.card-title a {
    color: inherit;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .novel-card {
        background: #2d3436;
        border-color: #444;
    }
    
    .novel-cover {
        background: #1a1a1a;
    }
    
    .card-img-top {
        background: #1a1a1a;
    }
    
    .card-title a {
        color: #f1f1f1;
    }
    
    .text-muted {
        color: #aaa !important;
    }
}

@media (max-width: 768px) {
    .card-img-top {
        height: 240px;
    }
}

@media (max-width: 576px) {
    .card-img-top {
        height: 200px;
    }
}
</style>

<?php get_footer(); ?>
