# LN Reader WordPress Theme

**Version:** 1.3.0
**Release Date:** July 21, 2025
**WordPress Compatibility:** 5.0+
**PHP Compatibility:** 7.4+

A comprehensive WordPress theme designed specifically for light novel reading websites with integrated Google OAuth authentication and comprehensive AdSense optimization.

## 🚀 Key Features

### 📚 Novel Management
- **Custom Post Types**: Dedicated novel and chapter management
- **Taxonomies**: Genres, tags, authors, and artists
- **Chapter Navigation**: Seamless reading experience
- **Import System**: XML import with comprehensive error handling
- **Reading Progress**: Track user reading progress
- **Bookmarks**: User bookmark system

### 🔐 Authentication System
- **Traditional Login**: Username/password authentication
- **Google OAuth**: Sign in with Google integration
- **User Registration**: Automated user creation
- **Account Linking**: Link Google accounts to existing users
- **Security Features**: Rate limiting, CSRF protection, activity logging

### 🎨 User Experience
- **Responsive Design**: Mobile-first approach
- **Bootstrap Integration**: Modern UI components
- **Dark/Light Theme**: Theme switching capability
- **Search Functionality**: AJAX-powered novel search
- **User Dashboard**: Personalized user experience

### 💰 AdSense Integration
- **Strategic Ad Placements**: 11 optimized widget areas
- **Google Site Kit Compatible**: Full Auto Ads support
- **Responsive Ad Units**: Mobile-optimized ad experience
- **Performance Optimized**: Minimal impact on page speed
- **Auto Ads Coordination**: Smart conflict prevention
- **Revenue Optimization**: Hybrid manual + auto placement strategy

## 📦 Installation

### Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher
- Modern web browser with JavaScript enabled

### Quick Install
1. Download the theme files
2. Upload to `/wp-content/themes/lnreader/`
3. Activate the theme in WordPress admin
4. Run the authentication setup (Tools → Auth Setup)
5. Configure Google OAuth (Settings → Google OAuth)

### Google OAuth Setup
1. Go to [Google Developers Console](https://console.developers.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 Client ID
5. Set redirect URI: `https://yoursite.com/oauth/google/callback`
6. Configure in WordPress: Settings → Google OAuth

### AdSense Setup
1. **Install Google Site Kit** (recommended for Auto Ads)
2. **Connect AdSense** through Site Kit dashboard
3. **Configure widget areas**: Appearance → Widgets
4. **Enable Auto Ads** for optimized placement
5. **Monitor performance** through admin bar status

For detailed AdSense setup, see `ADSENSE_INTEGRATION.md` and `SITE_KIT_SETUP_RECOMMENDATIONS.md`

## 🛠️ Configuration

### Theme Setup
- **Pages**: Login, Register, Dashboard, Reset Password pages auto-created
- **Menus**: Primary and footer menu locations
- **Widgets**: Custom widget areas for enhanced functionality
- **Permalinks**: Flush permalinks after activation

### Google OAuth Configuration
- **Client ID**: Your Google OAuth Client ID
- **Client Secret**: Your Google OAuth Client Secret
- **Redirect URI**: Automatically configured
- **Security**: Built-in rate limiting and validation

### AdSense Configuration
- **Widget Areas**: 11 strategic ad placement locations
- **Auto Ads**: Full Google Site Kit compatibility
- **Manual Placements**: Header, sidebar, footer, content areas
- **Mobile Optimization**: Responsive ad units
- **Performance**: Minimal impact on page speed
- **Admin Tools**: Status monitoring and configuration alerts

### Import System
- **XML Import**: Enhanced WordPress XML importer
- **Error Handling**: Comprehensive error logging and recovery
- **Progress Tracking**: Real-time import progress monitoring
- **Warning Management**: Dismissible import warnings

## 📁 File Structure

```
lnreader/
├── assets/                          # Theme assets
├── css/                            # Stylesheets
│   ├── adsense-styles.css         # AdSense optimization styles
│   ├── auth.css                   # Authentication styles
│   ├── chapter-buttons.css        # Chapter navigation styles
│   ├── footer.css                 # Footer styles
│   └── latest-releases.css        # Latest releases styles
├── includes/                       # PHP classes
│   └── GoogleOAuth.php            # OAuth handler
├── js/                            # JavaScript files
│   ├── auth.js                    # Authentication scripts
│   └── main.js                    # Main theme scripts
├── ADSENSE_INTEGRATION.md         # AdSense setup guide
├── GOOGLE_SITE_KIT_ADSENSE_COMPATIBILITY.md  # Site Kit compatibility
├── SITE_KIT_SETUP_RECOMMENDATIONS.md         # Setup recommendations
├── functions.php                  # Theme functions
├── sidebar.php                    # Sidebar with ad placements
├── style.css                      # Main stylesheet
└── README.md                      # This file
```

## 🔧 Customization

### Theme Options
- **Colors**: Customizable color scheme
- **Typography**: Google Fonts integration
- **Layout**: Flexible layout options
- **Social Links**: Configurable social media links

### Developer Hooks
```php
// Custom authentication actions
do_action('ln_reader_before_login');
do_action('ln_reader_after_login', $user);
do_action('ln_reader_oauth_success', $user, $provider);

// Import system hooks
do_action('ln_reader_import_start');
do_action('ln_reader_import_complete', $stats);
```

### CSS Customization
```css
/* Override theme colors */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
}

/* Custom button styles */
.btn-google {
    background-color: #db4437;
    border-color: #db4437;
}
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] Google OAuth login flow
- [ ] User registration via Google
- [ ] Traditional login/register
- [ ] Mobile responsiveness
- [ ] Cross-browser compatibility
- [ ] Import functionality
- [ ] Error handling

### Automated Testing
```bash
# Run PHP syntax check
php -l functions.php

# Check WordPress coding standards
phpcs --standard=WordPress functions.php
```

## 🐛 Troubleshooting

### Common Issues

#### Fatal Error: Function Not Found
**Solution:** Run `fix-fatal-error.php` script or clear import logs

#### Google OAuth Not Working
**Solutions:**
- Verify Client ID and Secret
- Check redirect URI configuration
- Ensure HTTPS is enabled
- Clear browser cache

#### Import Warnings
**Solutions:**
- Run `clear-import-warnings.php`
- Use Tools → Import Log → Clear Log
- Dismiss individual warnings

### Debug Mode
Enable WordPress debug mode in `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 📚 Documentation

### Available Documentation
- `CHANGELOG.md` - Version history and changes
- `GOOGLE_OAUTH_IMPLEMENTATION.md` - OAuth setup guide
- `IMPORT_WARNINGS_SOLUTION.md` - Import troubleshooting
- `FATAL_ERROR_FIX.md` - Error resolution guide

### API Reference
- **OAuth Functions**: `ln_reader_get_google_oauth_url()`, `ln_reader_is_google_oauth_enabled()`
- **User Functions**: `get_user_reading_progress()`, `has_user_rated()`
- **Import Functions**: `ln_reader_log_import_warning()`, `ln_reader_clear_import_log()`

## 🤝 Contributing

### Development Setup
1. Clone the repository
2. Set up local WordPress environment
3. Install development dependencies
4. Follow WordPress coding standards

### Reporting Issues
- Use GitHub issues for bug reports
- Include WordPress and PHP versions
- Provide detailed reproduction steps
- Include relevant error logs

## 📄 License

This theme is licensed under the GNU General Public License v2 or later.

## 🆘 Support

### Getting Help
- Check documentation files first
- Search existing GitHub issues
- Create new issue with detailed information
- Contact theme developer for premium support

### Version Support
- **v1.2.x**: Active development and support
- **v1.1.x**: Security updates only
- **v1.0.x**: End of life

---

**Made with ❤️ for the light novel reading community**
