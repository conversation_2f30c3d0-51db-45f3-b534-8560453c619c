/*
 * AdSense-friendly Styles for LN Reader Theme
 * Optimized for Google AdSense best practices
 */

/* General Ad Widget Styling */
.ad-widget {
    margin: 1.5rem 0;
    text-align: center;
    clear: both;
    position: relative;
}

.ad-widget:empty {
    display: none;
}

/* Ad Widget Labels */
.ad-widget .widget-title {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
    text-align: center;
    font-weight: 400;
}

/* Header Ad Placement */
.header-ads {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 0;
    margin-bottom: 1rem;
}

.header-ads .ad-widget {
    margin: 0;
}

/* Content Ad Placements */
.content-top-ads,
.content-middle-ads,
.content-bottom-ads {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    margin: 2rem 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    position: relative;
}

.content-top-ads {
    margin-top: 0;
}

.content-bottom-ads {
    margin-bottom: 0;
}

/* Sidebar Ad Placements */
.sidebar-top-ads,
.sidebar-middle-ads,
.sidebar-bottom-ads {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Footer Ad Placement */
.footer-ads {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 2rem 0;
    margin-top: 2rem;
    text-align: center;
}

/* Chapter-specific Ad Placements */
.chapter-top-ads,
.chapter-bottom-ads {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1.5rem;
    margin: 2rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Novel Page Ad Placements */
.novel-page-ads {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    margin: 1.5rem 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* AdSense Responsive Container */
.adsense-container {
    width: 100%;
    text-align: center;
    margin: 1rem 0;
    position: relative;
}

.adsense-container ins {
    display: block;
    margin: 0 auto;
}

/* Google AdSense Responsive Units */
.adsbygoogle {
    display: block;
    margin: 0 auto;
}

/* Standard AdSense Sizes */
.ad-leaderboard {
    width: 728px;
    height: 90px;
    max-width: 100%;
}

.ad-banner {
    width: 468px;
    height: 60px;
    max-width: 100%;
}

.ad-rectangle {
    width: 300px;
    height: 250px;
    max-width: 100%;
}

.ad-square {
    width: 250px;
    height: 250px;
    max-width: 100%;
}

.ad-skyscraper {
    width: 160px;
    height: 600px;
    max-width: 100%;
}

/* Mobile-specific Ad Styles */
@media (max-width: 768px) {
    .ad-widget {
        margin: 1rem 0;
    }
    
    .content-top-ads,
    .content-middle-ads,
    .content-bottom-ads,
    .chapter-top-ads,
    .chapter-bottom-ads {
        padding: 1rem;
        margin: 1.5rem 0;
    }
    
    .header-ads {
        padding: 0.75rem 0;
    }
    
    .footer-ads {
        padding: 1.5rem 0;
    }
    
    /* Mobile ad sizes */
    .ad-leaderboard,
    .ad-banner,
    .ad-rectangle,
    .ad-square,
    .ad-skyscraper {
        width: 100%;
        max-width: 320px;
        height: auto;
        min-height: 50px;
    }
}

/* Tablet-specific Ad Styles */
@media (min-width: 769px) and (max-width: 1024px) {
    .ad-leaderboard {
        width: 728px;
        height: 90px;
    }
    
    .ad-rectangle {
        width: 300px;
        height: 250px;
    }
}

/* Desktop Ad Styles */
@media (min-width: 1025px) {
    .sidebar .ad-widget {
        position: sticky;
        top: 20px;
    }
}

/* Reading Experience Protection */
.reading-container .ad-widget {
    max-width: 100%;
    overflow: hidden;
}

.chapter-content .ad-widget {
    margin: 2rem 0;
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
}

/* Ad Loading States */
.ad-widget.loading {
    min-height: 90px;
    background: #f8f9fa;
    position: relative;
}

.ad-widget.loading::before {
    content: "Advertisement";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #6c757d;
    font-size: 0.875rem;
}

/* AdSense Policy Compliance */
.ad-disclaimer {
    font-size: 0.75rem;
    color: #6c757d;
    text-align: center;
    margin-top: 0.5rem;
}

/* Ensure ads don't interfere with navigation */
.navbar + .header-ads {
    position: relative;
    z-index: 1;
}

/* Print styles - hide ads when printing */
@media print {
    .ad-widget,
    .header-ads,
    .footer-ads,
    .content-top-ads,
    .content-middle-ads,
    .content-bottom-ads,
    .sidebar-top-ads,
    .sidebar-middle-ads,
    .sidebar-bottom-ads,
    .chapter-top-ads,
    .chapter-bottom-ads,
    .novel-page-ads {
        display: none !important;
    }
}

/* Dark theme compatibility */
[data-theme="dark"] .ad-widget {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-theme="dark"] .header-ads,
[data-theme="dark"] .footer-ads {
    background-color: #1a202c;
    border-color: #2d3748;
}

/* Sepia theme compatibility */
[data-theme="sepia"] .ad-widget {
    background-color: #f7f3e9;
    border-color: #e8dcbf;
}

[data-theme="sepia"] .header-ads,
[data-theme="sepia"] .footer-ads {
    background-color: #f0e6d2;
    border-color: #e8dcbf;
}

/* Google Site Kit & Auto Ads Compatibility */

/* Auto Ads placed by Google */
.google-auto-placed {
    margin: 1.5rem 0 !important;
    text-align: center;
    clear: both;
    position: relative;
}

/* Auto Ads in chapter content - special handling */
.chapter-content .google-auto-placed,
.reading-container .google-auto-placed {
    margin: 2rem 0 !important;
    padding: 1rem 0;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    background: rgba(248, 249, 250, 0.5);
}

/* Auto Ads in sidebar */
.sidebar .google-auto-placed {
    margin: 1rem 0 !important;
    padding: 0.5rem;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Compatibility class for manual ads when Auto Ads is active */
.auto-ads-compatible {
    position: relative;
}

.auto-ads-compatible::before {
    content: "";
    display: block;
    height: 1px;
    background: transparent;
    margin: 0.5rem 0;
}

/* Mobile Auto Ads optimization */
@media (max-width: 768px) {
    .google-auto-placed {
        margin: 1rem 0 !important;
    }

    .chapter-content .google-auto-placed,
    .reading-container .google-auto-placed {
        margin: 1.5rem 0 !important;
        padding: 0.75rem 0;
    }

    .sidebar .google-auto-placed {
        margin: 0.75rem 0 !important;
        padding: 0.5rem;
    }
}

/* Auto Ads anchor ads (mobile) */
.google-auto-placed[data-anchor-status] {
    position: fixed !important;
    z-index: 9999;
    width: 100% !important;
    left: 0 !important;
}

/* Auto Ads vignette compatibility */
body.google-vignette-active {
    overflow: hidden;
}

/* Prevent Auto Ads from breaking theme layout */
.google-auto-placed ins {
    max-width: 100% !important;
    overflow: hidden;
}

/* Dark theme Auto Ads compatibility */
[data-theme="dark"] .google-auto-placed {
    background: rgba(26, 32, 44, 0.5) !important;
    border-color: #4a5568 !important;
}

[data-theme="dark"] .chapter-content .google-auto-placed,
[data-theme="dark"] .reading-container .google-auto-placed {
    background: rgba(26, 32, 44, 0.7) !important;
    border-color: #4a5568 !important;
}

/* Sepia theme Auto Ads compatibility */
[data-theme="sepia"] .google-auto-placed {
    background: rgba(240, 230, 210, 0.5) !important;
    border-color: #e8dcbf !important;
}

[data-theme="sepia"] .chapter-content .google-auto-placed,
[data-theme="sepia"] .reading-container .google-auto-placed {
    background: rgba(240, 230, 210, 0.7) !important;
    border-color: #e8dcbf !important;
}

/* Site Kit specific styles */
.googlesitekit-adsense-widget {
    margin: 1rem 0;
    text-align: center;
}

/* Prevent conflicts between manual and auto ads */
.ad-widget + .google-auto-placed,
.google-auto-placed + .ad-widget {
    margin-top: 2rem !important;
}

/* Auto Ads loading state */
.google-auto-placed:empty {
    min-height: 50px;
    background: #f8f9fa;
    position: relative;
}

.google-auto-placed:empty::before {
    content: "Advertisement";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #6c757d;
    font-size: 0.875rem;
    opacity: 0.7;
}

/* Ensure Auto Ads respect reading preferences */
.reading-container[data-font-size="small"] .google-auto-placed {
    font-size: 14px;
}

.reading-container[data-font-size="large"] .google-auto-placed {
    font-size: 18px;
}

.reading-container[data-font-size="extra-large"] .google-auto-placed {
    font-size: 20px;
}
