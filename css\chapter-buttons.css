.chapter-release-chapters {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 15px;
}

.chapter-release-item-chapter {
    margin: 0;
}

.chapter-release-chapter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    color: #495057;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.chapter-release-chapter:hover {
    background: #fff;
    border-color: #0d6efd;
    color: #0d6efd;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.1);
}

.chapter-release-chapter::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: #0d6efd;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chapter-release-chapter:hover::before {
    opacity: 1;
}

.chapter-number {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.chapter-number i {
    font-size: 0.875rem;
    color: #0d6efd;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.2s ease;
}

.chapter-release-chapter:hover .chapter-number i {
    opacity: 1;
    transform: translateX(0);
}

.chapter-release-time {
    font-size: 0.75rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 5px;
}

.chapter-release-time i {
    font-size: 0.875rem;
}

.coin-cost {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 8px;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 12px;
    color: #ffc107;
    font-weight: 600;
    font-size: 0.75rem;
    margin-left: 8px;
}

.coin-cost i {
    font-size: 0.75rem;
}
