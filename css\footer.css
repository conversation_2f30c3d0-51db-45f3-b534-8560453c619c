footer.site-footer {
    background-color: #2c3338;
    color: #fff;
    padding: 40px 0 20px;
    margin-top: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h5 {
    color: #fff;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-section h5:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 40px;
    height: 2px;
    background: #0d6efd;
}

.footer-section p {
    color: #a7aaad;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 15px;
}

.footer-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-menu li {
    margin-bottom: 10px;
}

.footer-menu a {
    color: #a7aaad;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
    display: block;
    padding: 4px 0;
}

.footer-menu a:hover {
    color: #fff;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    color: #a7aaad;
    text-decoration: none;
    font-size: 1.2rem;
    transition: color 0.2s ease;
}

.social-links a:hover {
    color: #fff;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.footer-bottom p {
    color: #a7aaad;
    font-size: 0.85rem;
    margin: 0;
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .footer-section {
        text-align: center;
    }
    
    .footer-section h5:after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .social-links {
        justify-content: center;
    }
}
