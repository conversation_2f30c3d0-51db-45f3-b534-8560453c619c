.latest-releases {
    padding: 20px 0;
}

.latest-releases-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 0 10px;
}

.latest-releases-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: #333;
}

.latest-releases-header .tabs {
    display: flex;
    gap: 10px;
}

.latest-releases-header .tab {
    padding: 6px 16px;
    border-radius: 20px;
    cursor: pointer;
    background: #f0f0f0;
    color: #666;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    border: 1px solid transparent;
}

.latest-releases-header .tab:hover {
    background: #e0e0e0;
    color: #333;
    text-decoration: none;
}

.latest-releases-header .tab.active {
    background: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

.chapter-release-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    padding: 0 10px;
}

.chapter-release-item {
    display: flex;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.chapter-release-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.chapter-release-cover {
    width: 120px;
    min-width: 120px;
    height: 160px;
    position: relative;
}

.chapter-release-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.chapter-release-cover .placeholder-cover {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #adb5bd;
    font-size: 2rem;
}

.chapter-release-info {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.chapter-release-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
    text-decoration: none;
    line-height: 1.4;
}

.chapter-release-title:hover {
    color: #0d6efd;
    text-decoration: none;
}

.chapter-release-rating {
    margin-bottom: 15px;
}

.chapter-release-rating > div {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 0.875rem;
    padding: 4px 8px;
    border-radius: 4px;
    background: rgba(0,0,0,0.03);
}

.chapter-release-rating span {
    margin-left: 5px;
    font-weight: 600;
}

.rating-excellent { color: #28a745; }
.rating-great { color: #17a2b8; }
.rating-good { color: #ffc107; }
.rating-fair { color: #fd7e14; }
.rating-poor { color: #dc3545; }

.chapter-release-chapters {
    margin-top: auto;
}

.chapter-release-item-chapter {
    margin-bottom: 10px;
}

.chapter-release-item-chapter:last-child {
    margin-bottom: 0;
}

.chapter-release-chapter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #666;
    text-decoration: none;
    font-size: 0.875rem;
    padding: 6px 10px;
    border-radius: 4px;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.chapter-release-chapter:hover {
    color: #0d6efd;
    text-decoration: none;
    background: #f0f4f8;
}

.chapter-number {
    display: flex;
    align-items: center;
    gap: 6px;
}

.chapter-number i {
    font-size: 0.75rem;
}

.coin-cost {
    font-size: 0.75rem;
    color: #ffc107;
    margin-left: 8px;
    font-weight: 600;
}

.chapter-release-time {
    font-size: 0.75rem;
    color: #999;
}

/* Popular Section */
.popular-section {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.popular-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.popular-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    color: #333;
}

.popular-item {
    display: flex;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.2s ease;
}

.popular-item:hover {
    background-color: #f8f9fa;
}

.popular-item:last-child {
    border-bottom: none;
}

.popular-item-cover {
    width: 80px;
    min-width: 80px;
    height: 120px;
    margin-right: 15px;
}

.popular-item-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.popular-item-info {
    flex: 1;
}

.popular-item-title {
    display: block;
    font-size: 0.9375rem;
    font-weight: 600;
    color: #333;
    text-decoration: none;
    margin-bottom: 10px;
    line-height: 1.4;
}

.popular-item-title:hover {
    color: #0d6efd;
    text-decoration: none;
}

.popular-item-chapter {
    font-size: 0.875rem;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
}

.popular-item-chapter a {
    color: #666;
    text-decoration: none;
    display: block;
    margin-bottom: 4px;
}

.popular-item-chapter a:hover {
    color: #0d6efd;
}

.popular-item-time {
    font-size: 0.75rem;
    color: #999;
}

/* Responsive */
@media (max-width: 992px) {
    .chapter-release-grid {
        grid-template-columns: 1fr;
    }
    
    .popular-section {
        margin-top: 30px;
    }
}

@media (max-width: 576px) {
    .latest-releases-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .chapter-release-cover {
        width: 100px;
        min-width: 100px;
        height: 140px;
    }
    
    .chapter-release-info {
        padding: 12px;
    }
    
    .chapter-release-title {
        font-size: 0.9375rem;
    }
}
