<?php
/**
 * Debug file untuk troubleshoot masalah chapter
 * Akses: yoursite.com/wp-content/themes/ln-reader/debug-chapters.php?novel_id=123
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin only.');
}

$novel_id = isset($_GET['novel_id']) ? intval($_GET['novel_id']) : 0;

if (!$novel_id) {
    die('Please provide novel_id parameter. Example: ?novel_id=123');
}

$novel = get_post($novel_id);
if (!$novel) {
    die('Novel not found with ID: ' . $novel_id);
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Chapter Debug - <?php echo esc_html($novel->post_title); ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .debug-section h3 { margin-top: 0; color: #333; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .code { background: #f8f9fa; padding: 10px; font-family: monospace; }
    </style>
</head>
<body>

<h1>Chapter Debug untuk Novel: <?php echo esc_html($novel->post_title); ?></h1>
<p><strong>Novel ID:</strong> <?php echo $novel_id; ?></p>
<p><strong>Novel Slug:</strong> <?php echo $novel->post_name; ?></p>

<?php
// Method 1: Check posts with _novel_id meta
echo '<div class="debug-section">';
echo '<h3>Method 1: Posts dengan _novel_id meta field</h3>';

$chapters_method1 = get_posts([
    'post_type' => 'post',
    'meta_query' => [
        [
            'key' => '_novel_id',
            'value' => $novel_id,
            'compare' => '='
        ]
    ],
    'posts_per_page' => -1,
    'post_status' => 'publish'
]);

echo '<p><strong>Jumlah ditemukan:</strong> ' . count($chapters_method1) . '</p>';

if (!empty($chapters_method1)) {
    echo '<table>';
    echo '<tr><th>ID</th><th>Title</th><th>_novel_id</th><th>_chapter_number</th><th>Date</th></tr>';
    foreach ($chapters_method1 as $chapter) {
        $novel_id_meta = get_post_meta($chapter->ID, '_novel_id', true);
        $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
        echo '<tr>';
        echo '<td>' . $chapter->ID . '</td>';
        echo '<td>' . esc_html($chapter->post_title) . '</td>';
        echo '<td>' . esc_html($novel_id_meta) . '</td>';
        echo '<td>' . esc_html($chapter_number) . '</td>';
        echo '<td>' . $chapter->post_date . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p class="error">Tidak ada posts ditemukan dengan _novel_id = ' . $novel_id . '</p>';
}
echo '</div>';

// Method 2: Check all posts with _novel_id meta (any value)
echo '<div class="debug-section">';
echo '<h3>Method 2: Semua posts dengan _novel_id meta field</h3>';

$all_novel_posts = get_posts([
    'post_type' => 'post',
    'meta_key' => '_novel_id',
    'posts_per_page' => 20,
    'post_status' => 'publish'
]);

echo '<p><strong>Total posts dengan _novel_id:</strong> ' . count($all_novel_posts) . '</p>';

if (!empty($all_novel_posts)) {
    echo '<table>';
    echo '<tr><th>ID</th><th>Title</th><th>_novel_id</th><th>_chapter_number</th></tr>';
    foreach ($all_novel_posts as $post) {
        $novel_id_meta = get_post_meta($post->ID, '_novel_id', true);
        $chapter_number = get_post_meta($post->ID, '_chapter_number', true);
        echo '<tr>';
        echo '<td>' . $post->ID . '</td>';
        echo '<td>' . esc_html($post->post_title) . '</td>';
        echo '<td>' . esc_html($novel_id_meta) . '</td>';
        echo '<td>' . esc_html($chapter_number) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}
echo '</div>';

// Method 3: Search by novel slug
echo '<div class="debug-section">';
echo '<h3>Method 3: Search berdasarkan novel slug</h3>';

$chapters_method3 = get_posts([
    'post_type' => 'post',
    'posts_per_page' => 10,
    's' => $novel->post_name,
    'post_status' => 'publish'
]);

echo '<p><strong>Search term:</strong> "' . esc_html($novel->post_name) . '"</p>';
echo '<p><strong>Jumlah ditemukan:</strong> ' . count($chapters_method3) . '</p>';

if (!empty($chapters_method3)) {
    echo '<table>';
    echo '<tr><th>ID</th><th>Title</th><th>Content Preview</th></tr>';
    foreach ($chapters_method3 as $chapter) {
        echo '<tr>';
        echo '<td>' . $chapter->ID . '</td>';
        echo '<td>' . esc_html($chapter->post_title) . '</td>';
        echo '<td>' . esc_html(wp_trim_words($chapter->post_content, 10)) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}
echo '</div>';

// Method 4: Check categories and tags
echo '<div class="debug-section">';
echo '<h3>Method 4: Categories dan Tags</h3>';

$novel_categories = wp_get_post_categories($novel_id);
$novel_tags = wp_get_post_tags($novel_id, ['fields' => 'ids']);

echo '<p><strong>Novel Categories:</strong> ' . implode(', ', $novel_categories) . '</p>';
echo '<p><strong>Novel Tags:</strong> ' . implode(', ', $novel_tags) . '</p>';

if (!empty($novel_categories) || !empty($novel_tags)) {
    $tax_query = ['relation' => 'OR'];
    
    if (!empty($novel_categories)) {
        $tax_query[] = [
            'taxonomy' => 'category',
            'field' => 'term_id',
            'terms' => $novel_categories
        ];
    }
    
    if (!empty($novel_tags)) {
        $tax_query[] = [
            'taxonomy' => 'post_tag',
            'field' => 'term_id',
            'terms' => $novel_tags
        ];
    }
    
    $chapters_method4 = get_posts([
        'post_type' => 'post',
        'posts_per_page' => 10,
        'tax_query' => $tax_query,
        'post_status' => 'publish',
        'post__not_in' => [$novel_id]
    ]);
    
    echo '<p><strong>Posts dengan category/tag sama:</strong> ' . count($chapters_method4) . '</p>';
    
    if (!empty($chapters_method4)) {
        echo '<table>';
        echo '<tr><th>ID</th><th>Title</th><th>Categories</th><th>Tags</th></tr>';
        foreach ($chapters_method4 as $chapter) {
            $cats = wp_get_post_categories($chapter->ID);
            $tags = wp_get_post_tags($chapter->ID, ['fields' => 'names']);
            echo '<tr>';
            echo '<td>' . $chapter->ID . '</td>';
            echo '<td>' . esc_html($chapter->post_title) . '</td>';
            echo '<td>' . implode(', ', $cats) . '</td>';
            echo '<td>' . implode(', ', $tags) . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
}
echo '</div>';

// Method 5: Using the theme function
echo '<div class="debug-section">';
echo '<h3>Method 5: Menggunakan ln_reader_get_novel_chapters()</h3>';

if (function_exists('ln_reader_get_novel_chapters')) {
    $chapters_method5 = ln_reader_get_novel_chapters($novel_id);
    echo '<p><strong>Jumlah ditemukan:</strong> ' . count($chapters_method5) . '</p>';
    
    if (!empty($chapters_method5)) {
        echo '<table>';
        echo '<tr><th>ID</th><th>Title</th><th>Date</th></tr>';
        foreach ($chapters_method5 as $chapter) {
            echo '<tr>';
            echo '<td>' . $chapter->ID . '</td>';
            echo '<td>' . esc_html($chapter->post_title) . '</td>';
            echo '<td>' . $chapter->post_date . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
} else {
    echo '<p class="error">Function ln_reader_get_novel_chapters() tidak ditemukan!</p>';
}
echo '</div>';

// Recommendations
echo '<div class="debug-section">';
echo '<h3>Rekomendasi</h3>';

if (count($chapters_method1) > 0) {
    echo '<p class="success">✅ Method 1 berhasil! Chapters ditemukan menggunakan _novel_id meta field.</p>';
} elseif (count($chapters_method3) > 0) {
    echo '<p class="warning">⚠️ Method 1 gagal, tapi Method 3 berhasil. Pertimbangkan untuk menambahkan _novel_id meta field ke posts yang ditemukan.</p>';
    echo '<div class="code">';
    echo 'Untuk menambahkan _novel_id ke posts yang ditemukan:<br>';
    echo 'foreach ($chapters_method3 as $chapter) {<br>';
    echo '&nbsp;&nbsp;&nbsp;&nbsp;update_post_meta($chapter->ID, "_novel_id", ' . $novel_id . ');<br>';
    echo '}';
    echo '</div>';
} else {
    echo '<p class="error">❌ Tidak ada chapters ditemukan dengan semua method. Periksa struktur data Anda.</p>';
}

echo '</div>';
?>

</body>
</html>
