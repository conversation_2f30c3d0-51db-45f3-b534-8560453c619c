        </div><!-- .container -->
    </main><!-- #main -->

    <!-- Footer Ad Placement -->
    <?php if (is_active_sidebar('footer-ads')) : ?>
        <div class="footer-ads" role="complementary" aria-label="<?php esc_attr_e('Footer advertisements', 'ln-reader'); ?>">
            <div class="container">
                <?php dynamic_sidebar('footer-ads'); ?>
            </div>
        </div>
    <?php endif; ?>

    <footer id="colophon" class="site-footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h2 class="footer-title"><?php bloginfo('name'); ?></h2>
                    <p class="footer-description">
                        <?php
                        $description = get_bloginfo('description');
                        echo $description ? esc_html($description) : esc_html__('Read the latest light novels and web novels in high quality. New chapters updated daily.', 'ln-reader');
                        ?>
                    </p>

                    <!-- Structured Data for Organization -->
                    <script type="application/ld+json">
                    {
                        "@context": "https://schema.org",
                        "@type": "Organization",
                        "name": "<?php echo esc_js(get_bloginfo('name')); ?>",
                        "url": "<?php echo esc_url(home_url()); ?>",
                        "description": "<?php echo esc_js($description ?: __('Read the latest light novels and web novels in high quality. New chapters updated daily.', 'ln-reader')); ?>",
                        "sameAs": [
                            "<?php echo esc_url(home_url()); ?>"
                        ]
                    }
                    </script>
                </div>

                <div class="footer-section">
                    <h3 class="footer-section-title"><?php esc_html_e('Quick Links', 'ln-reader'); ?></h3>
                    <nav class="footer-navigation" role="navigation" aria-label="<?php esc_attr_e('Footer Navigation', 'ln-reader'); ?>">
                        <?php
                        wp_nav_menu(array(
                            'theme_location' => 'footer',
                            'depth' => 1,
                            'container' => false,
                            'menu_class' => 'footer-menu',
                            'fallback_cb' => function() {
                                echo '<ul class="footer-menu">';
                                echo '<li><a href="' . esc_url(home_url('/bookmarks')) . '">' . esc_html__('Bookmarks', 'ln-reader') . '</a></li>';
                                echo '<li><a href="' . esc_url(get_post_type_archive_link('novel')) . '">' . esc_html__('All Novels', 'ln-reader') . '</a></li>';
                                echo '<li><a href="' . esc_url(home_url('/contact')) . '">' . esc_html__('Contact', 'ln-reader') . '</a></li>';
                                echo '<li><a href="' . esc_url(home_url('/privacy-policy')) . '">' . esc_html__('Privacy Policy', 'ln-reader') . '</a></li>';
                                echo '</ul>';
                            }
                        ));
                        ?>
                    </nav>
                </div>

                <div class="footer-section">
                    <h3 class="footer-section-title"><?php esc_html_e('Connect With Us', 'ln-reader'); ?></h3>
                    <div class="social-links" role="list">
                        <a href="#" class="social-link" title="<?php esc_attr_e('Follow us on Discord', 'ln-reader'); ?>" aria-label="<?php esc_attr_e('Discord', 'ln-reader'); ?>">
                            <i class="bi bi-discord" aria-hidden="true"></i>
                            <span class="screen-reader-text"><?php esc_html_e('Discord', 'ln-reader'); ?></span>
                        </a>
                        <a href="#" class="social-link" title="<?php esc_attr_e('Follow us on Twitter', 'ln-reader'); ?>" aria-label="<?php esc_attr_e('Twitter', 'ln-reader'); ?>">
                            <i class="bi bi-twitter" aria-hidden="true"></i>
                            <span class="screen-reader-text"><?php esc_html_e('Twitter', 'ln-reader'); ?></span>
                        </a>
                        <a href="#" class="social-link" title="<?php esc_attr_e('Follow us on Facebook', 'ln-reader'); ?>" aria-label="<?php esc_attr_e('Facebook', 'ln-reader'); ?>">
                            <i class="bi bi-facebook" aria-hidden="true"></i>
                            <span class="screen-reader-text"><?php esc_html_e('Facebook', 'ln-reader'); ?></span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p class="copyright">
                        &copy; <?php echo esc_html(date('Y')); ?>
                        <a href="<?php echo esc_url(home_url()); ?>" rel="home"><?php bloginfo('name'); ?></a>.
                        <?php esc_html_e('All rights reserved.', 'ln-reader'); ?>
                    </p>

                    <?php if (current_user_can('administrator')) : ?>
                        <div class="admin-info">
                            <small>
                                <?php esc_html_e('LN Reader Theme', 'ln-reader'); ?> v<?php echo esc_html(LN_READER_VERSION); ?>
                                <?php if (function_exists('ln_reader_is_google_oauth_enabled') && ln_reader_is_google_oauth_enabled()) : ?>
                                    | <?php esc_html_e('Google OAuth:', 'ln-reader'); ?> <span class="status-enabled">✓ <?php esc_html_e('Enabled', 'ln-reader'); ?></span>
                                <?php else : ?>
                                    | <?php esc_html_e('Google OAuth:', 'ln-reader'); ?> <span class="status-disabled">✗ <?php esc_html_e('Disabled', 'ln-reader'); ?></span>
                                <?php endif; ?>
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </footer>

    <?php wp_footer(); ?>
</body>
</html>
