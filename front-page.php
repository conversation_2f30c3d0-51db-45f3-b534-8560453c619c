<?php get_header(); ?>

<!-- Breadcrumb Navigation -->
<?php ln_reader_breadcrumbs(); ?>

<!-- Hero Section -->
<section class="hero-section" role="banner">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title"><?php esc_html_e('Discover Amazing Light Novels', 'ln-reader'); ?></h1>
            <p class="hero-description">
                <?php esc_html_e('Read the latest light novels and web novels in high quality. New chapters updated daily with the best reading experience.', 'ln-reader'); ?>
            </p>
            <div class="hero-actions">
                <a href="<?php echo esc_url(get_post_type_archive_link('novel')); ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-book" aria-hidden="true"></i> <?php esc_html_e('Browse All Novels', 'ln-reader'); ?>
                </a>
                <?php if (!is_user_logged_in()) : ?>
                    <a href="<?php echo esc_url(home_url('/register')); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-user-plus" aria-hidden="true"></i> <?php esc_html_e('Join Now', 'ln-reader'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Content Top Ads -->
<?php if (is_active_sidebar('content-top-ads')) : ?>
    <div class="content-top-ads" role="complementary" aria-label="<?php esc_attr_e('Top advertisements', 'ln-reader'); ?>">
        <div class="container">
            <?php dynamic_sidebar('content-top-ads'); ?>
        </div>
    </div>
<?php endif; ?>

<div class="container">
    <!-- Latest Updates -->
    <section class="latest-updates section-spacing" role="region" aria-labelledby="latest-updates-heading">
        <header class="section-header">
            <h2 id="latest-updates-heading" class="section-title">
                <i class="fas fa-clock" aria-hidden="true"></i>
                <?php esc_html_e('Latest Updates', 'ln-reader'); ?>
            </h2>
            <a href="<?php echo esc_url(get_post_type_archive_link('novel')); ?>" class="btn btn-outline-primary">
                <?php esc_html_e('View All Novels', 'ln-reader'); ?>
                <i class="fas fa-arrow-right" aria-hidden="true"></i>
            </a>
        </header>

        <div class="row g-4">
            <!-- Latest Novels Grid -->
            <div class="col-lg-8">
                <div class="novels-grid" role="list" aria-label="<?php esc_attr_e('Latest updated novels', 'ln-reader'); ?>">
                    <?php
                    $latest = new WP_Query([
                        'post_type' => 'novel',
                        'posts_per_page' => 6,
                        'orderby' => 'modified',
                        'order' => 'DESC',
                        'meta_query' => [
                            [
                                'key' => '_novel_status',
                                'value' => ['ongoing', 'completed'],
                                'compare' => 'IN'
                            ]
                        ]
                    ]);
                    if ($latest->have_posts()) :
                        while ($latest->have_posts()) : $latest->the_post();
                    ?>
                    <article class="novel-card-item" role="listitem">
                        <div class="novel-card">
                            <div class="novel-cover-wrapper">
                                <?php if (has_post_thumbnail()) : ?>
                                    <a href="<?php the_permalink(); ?>" class="novel-cover-link" aria-label="<?php echo esc_attr(sprintf(__('Read %s', 'ln-reader'), get_the_title())); ?>">
                                        <?php the_post_thumbnail('novel-card', [
                                            'class' => 'novel-cover-image',
                                            'alt' => esc_attr(get_the_title()),
                                            'loading' => 'lazy'
                                        ]); ?>
                                    </a>
                                <?php else : ?>
                                    <div class="novel-cover-placeholder">
                                        <i class="fas fa-book" aria-hidden="true"></i>
                                    </div>
                                <?php endif; ?>

                                <?php
                                $status = get_post_meta(get_the_ID(), '_novel_status', true);
                                if ($status) : ?>
                                    <span class="novel-status novel-status--<?php echo esc_attr(strtolower($status)); ?>" aria-label="<?php echo esc_attr(sprintf(__('Status: %s', 'ln-reader'), ucfirst($status))); ?>">
                                        <?php echo esc_html(ucfirst($status)); ?>
                                    </span>
                                <?php endif; ?>
                            </div>

                            <div class="novel-card-content">
                                <h3 class="novel-title">
                                    <a href="<?php the_permalink(); ?>" class="novel-title-link">
                                        <?php the_title(); ?>
                                    </a>
                                </h3>

                                <div class="novel-meta">
                                    <?php
                                    $author = get_post_meta(get_the_ID(), '_author', true) ?: get_post_meta(get_the_ID(), '_novel_author', true);
                                    if ($author) : ?>
                                        <div class="novel-author">
                                            <i class="fas fa-user" aria-hidden="true"></i>
                                            <span><?php echo esc_html($author); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <?php
                                    $rating = get_novel_rating(get_the_ID());
                                    $average = isset($rating['average']) ? floatval($rating['average']) : 0;
                                    $count = isset($rating['count']) ? intval($rating['count']) : 0;
                                    if ($average > 0) : ?>
                                        <div class="novel-rating" aria-label="<?php echo esc_attr(sprintf(__('Rating: %s out of 5 stars', 'ln-reader'), $average)); ?>">
                                            <div class="rating-stars" aria-hidden="true">
                                                <?php for ($i = 1; $i <= 5; $i++) : ?>
                                                    <i class="fas fa-star <?php echo $i <= $average ? 'star-filled' : 'star-empty'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                            <span class="rating-text">
                                                <?php echo number_format($average, 1); ?> (<?php echo $count; ?>)
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="novel-chapters">
                                    <?php
                                    // Get latest chapters
                                    $chapters = get_posts([
                                        'post_type' => 'post',
                                        'posts_per_page' => 2,
                                        'meta_key' => '_novel_id',
                                        'meta_value' => get_the_ID(),
                                        'orderby' => 'date',
                                        'order' => 'DESC'
                                    ]);

                                    if ($chapters) :
                                        foreach ($chapters as $chapter) :
                                            $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                                            $chapter_url = get_permalink($chapter->ID);
                                            $time_diff = human_time_diff(strtotime($chapter->post_date), current_time('timestamp'));
                                            ?>
                                            <a href="<?php echo esc_url($chapter_url); ?>" class="chapter-link" aria-label="<?php echo esc_attr(sprintf(__('Read Chapter %s', 'ln-reader'), $chapter_number)); ?>">
                                                <span class="chapter-number"><?php esc_html_e('Chapter', 'ln-reader'); ?> <?php echo esc_html($chapter_number); ?></span>
                                                <time class="chapter-time" datetime="<?php echo esc_attr(get_the_date('c', $chapter->ID)); ?>">
                                                    <?php echo esc_html($time_diff); ?> <?php esc_html_e('ago', 'ln-reader'); ?>
                                                </time>
                                            </a>
                                            <?php
                                        endforeach;
                                    endif;
                                    wp_reset_postdata();
                                    ?>
                                </div>
                            </div>
                        </div>
                    </article>
                    <?php
                    endwhile;
                    else : ?>
                        <div class="no-novels-message">
                            <i class="fas fa-book-open" aria-hidden="true"></i>
                            <h3><?php esc_html_e('No novels available yet', 'ln-reader'); ?></h3>
                            <p><?php esc_html_e('Check back soon for new content!', 'ln-reader'); ?></p>
                        </div>
                    <?php endif;
                    wp_reset_postdata();
                    ?>
                </div>
            </div>

            <!-- Popular This Week -->
            <div class="col-lg-4">
                <aside class="popular-section" role="complementary" aria-labelledby="popular-heading">
                    <div class="section-card">
                        <header class="section-card-header">
                            <h3 id="popular-heading" class="section-card-title">
                                <i class="fas fa-fire" aria-hidden="true"></i>
                                <?php esc_html_e('Popular This Week', 'ln-reader'); ?>
                            </h3>
                        </header>
                        <div class="popular-list" role="list">
                            <?php
                            $popular = new WP_Query([
                                'post_type' => 'novel',
                                'posts_per_page' => 5,
                                'meta_key' => '_novel_views',
                                'orderby' => 'meta_value_num',
                                'order' => 'DESC',
                                'date_query' => [
                                    [
                                        'after' => '1 week ago'
                                    ]
                                ]
                            ]);

                            if ($popular->have_posts()) :
                                $rank = 1;
                                while ($popular->have_posts()) : $popular->the_post();
                            ?>
                            <article class="popular-item" role="listitem">
                                <a href="<?php the_permalink(); ?>" class="popular-link" aria-label="<?php echo esc_attr(sprintf(__('Read %s - Rank %d', 'ln-reader'), get_the_title(), $rank)); ?>">
                                    <div class="popular-rank" aria-hidden="true">
                                        <span class="rank-number"><?php echo $rank; ?></span>
                                    </div>

                                    <?php if (has_post_thumbnail()) : ?>
                                        <div class="popular-cover">
                                            <?php the_post_thumbnail('novel-thumb', [
                                                'class' => 'popular-cover-image',
                                                'alt' => esc_attr(get_the_title()),
                                                'loading' => 'lazy'
                                            ]); ?>
                                        </div>
                                    <?php endif; ?>

                                    <div class="popular-content">
                                        <h4 class="popular-title"><?php the_title(); ?></h4>
                                        <div class="popular-meta">
                                            <?php
                                            $views = get_post_meta(get_the_ID(), '_novel_views', true);
                                            if ($views) : ?>
                                                <span class="popular-views">
                                                    <i class="fas fa-eye" aria-hidden="true"></i>
                                                    <?php echo number_format($views); ?>
                                                </span>
                                            <?php endif; ?>

                                            <?php
                                            $latest_chapter = get_posts([
                                                'post_type' => 'post',
                                                'posts_per_page' => 1,
                                                'meta_key' => '_novel_id',
                                                'meta_value' => get_the_ID(),
                                                'orderby' => 'date',
                                                'order' => 'DESC'
                                            ]);

                                            if ($latest_chapter) :
                                                $chapter_number = get_post_meta($latest_chapter[0]->ID, '_chapter_number', true);
                                            ?>
                                                <span class="popular-chapter">
                                                    <?php esc_html_e('Ch.', 'ln-reader'); ?> <?php echo esc_html($chapter_number); ?>
                                                </span>
                                            <?php
                                            endif;
                                            wp_reset_postdata();
                                            ?>
                                        </div>
                                    </div>
                                </a>
                            </article>
                            <?php
                            $rank++;
                            endwhile;
                            else : ?>
                                <div class="no-popular-message">
                                    <p><?php esc_html_e('No popular novels this week yet.', 'ln-reader'); ?></p>
                                </div>
                            <?php endif;
                            wp_reset_postdata();
                            ?>
                        </div>
                    </div>
                </aside>
            </div>
        </div>
    </section>

    <!-- Content Middle Ads -->
    <?php if (is_active_sidebar('content-middle-ads')) : ?>
        <div class="content-middle-ads" role="complementary" aria-label="<?php esc_attr_e('Middle advertisements', 'ln-reader'); ?>">
            <?php dynamic_sidebar('content-middle-ads'); ?>
        </div>
    <?php endif; ?>

    <!-- Browse by Genre -->
    <section class="genres-section section-spacing" role="region" aria-labelledby="genres-heading">
        <header class="section-header">
            <h2 id="genres-heading" class="section-title">
                <i class="fas fa-tags" aria-hidden="true"></i>
                <?php esc_html_e('Browse by Genre', 'ln-reader'); ?>
            </h2>
            <p class="section-description">
                <?php esc_html_e('Discover novels by your favorite genres', 'ln-reader'); ?>
            </p>
        </header>

        <div class="genres-grid" role="list">
            <?php
            // Define genre icons and colors with better accessibility
            $genre_styles = [
                'action' => ['icon' => 'fas fa-fist-raised', 'color' => '#e74c3c', 'bg' => '#fdf2f2'],
                'adventure' => ['icon' => 'fas fa-compass', 'color' => '#3498db', 'bg' => '#f0f9ff'],
                'comedy' => ['icon' => 'fas fa-laugh', 'color' => '#f39c12', 'bg' => '#fffbf0'],
                'drama' => ['icon' => 'fas fa-theater-masks', 'color' => '#9b59b6', 'bg' => '#faf5ff'],
                'fantasy' => ['icon' => 'fas fa-dragon', 'color' => '#2ecc71', 'bg' => '#f0fff4'],
                'horror' => ['icon' => 'fas fa-ghost', 'color' => '#95a5a6', 'bg' => '#f8f9fa'],
                'mystery' => ['icon' => 'fas fa-search', 'color' => '#34495e', 'bg' => '#f8f9fa'],
                'romance' => ['icon' => 'fas fa-heart', 'color' => '#e84393', 'bg' => '#fef7f7'],
                'sci-fi' => ['icon' => 'fas fa-rocket', 'color' => '#00cec9', 'bg' => '#f0fdfa'],
                'slice-of-life' => ['icon' => 'fas fa-coffee', 'color' => '#fdcb6e', 'bg' => '#fffbf0'],
                'sports' => ['icon' => 'fas fa-football-ball', 'color' => '#2980b9', 'bg' => '#f0f9ff'],
                'supernatural' => ['icon' => 'fas fa-magic', 'color' => '#8e44ad', 'bg' => '#faf5ff']
            ];

            $genres = get_terms([
                'taxonomy' => 'novel_genre',
                'hide_empty' => true,
                'number' => 12,
                'orderby' => 'count',
                'order' => 'DESC'
            ]);

            if ($genres && !is_wp_error($genres)) :
                foreach ($genres as $genre) :
                    $genre_slug = $genre->slug;
                    $style = isset($genre_styles[$genre_slug]) ? $genre_styles[$genre_slug] : [
                        'icon' => 'fas fa-bookmark',
                        'color' => 'var(--primary-color)',
                        'bg' => 'var(--background-secondary)'
                    ];
            ?>
            <article class="genre-card" role="listitem">
                <a href="<?php echo esc_url(get_term_link($genre)); ?>"
                   class="genre-link"
                   aria-label="<?php echo esc_attr(sprintf(__('Browse %s novels (%d available)', 'ln-reader'), $genre->name, $genre->count)); ?>"
                   style="--genre-color: <?php echo esc_attr($style['color']); ?>; --genre-bg: <?php echo esc_attr($style['bg']); ?>;">
                    <div class="genre-icon" aria-hidden="true">
                        <i class="<?php echo esc_attr($style['icon']); ?>"></i>
                    </div>
                    <div class="genre-content">
                        <h3 class="genre-name"><?php echo esc_html($genre->name); ?></h3>
                        <span class="genre-count">
                            <?php echo esc_html(sprintf(_n('%d Novel', '%d Novels', $genre->count, 'ln-reader'), $genre->count)); ?>
                        </span>
                    </div>
                </a>
            </article>
            <?php
                endforeach;
            else : ?>
                <div class="no-genres-message">
                    <p><?php esc_html_e('No genres available yet.', 'ln-reader'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Content Bottom Ads -->
    <?php if (is_active_sidebar('content-bottom-ads')) : ?>
        <div class="content-bottom-ads" role="complementary" aria-label="<?php esc_attr_e('Bottom advertisements', 'ln-reader'); ?>">
            <?php dynamic_sidebar('content-bottom-ads'); ?>
        </div>
    <?php endif; ?>
</div>

<!-- Structured Data for Website -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "<?php echo esc_js(get_bloginfo('name')); ?>",
    "url": "<?php echo esc_url(home_url()); ?>",
    "description": "<?php echo esc_js(get_bloginfo('description')); ?>",
    "potentialAction": {
        "@type": "SearchAction",
        "target": {
            "@type": "EntryPoint",
            "urlTemplate": "<?php echo esc_url(home_url('/?s={search_term_string}&post_type=novel')); ?>"
        },
        "query-input": "required name=search_term_string"
    },
    "publisher": {
        "@type": "Organization",
        "name": "<?php echo esc_js(get_bloginfo('name')); ?>",
        "url": "<?php echo esc_url(home_url()); ?>"
    }
}
</script>

<style>
/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: var(--spacing-2xl) 0;
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-content {
    text-align: center;
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-description {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
    line-height: var(--line-height-relaxed);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.hero-actions .btn {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.hero-actions .btn-primary {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
}

.hero-actions .btn-primary:hover {
    background: transparent;
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.hero-actions .btn-outline-primary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.hero-actions .btn-outline-primary:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Section Styling */
.section-spacing {
    margin-bottom: var(--spacing-2xl);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-light);
}

.section-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-title i {
    color: var(--primary-color);
}

.section-description {
    color: var(--text-secondary);
    margin: var(--spacing-xs) 0 0 0;
    font-size: var(--font-size-base);
}

/* Novel Cards Grid */
.novels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.novel-card-item {
    position: relative;
}

.novel-card {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-normal);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.novel-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
}

.novel-cover-wrapper {
    position: relative;
    aspect-ratio: 3/4;
    overflow: hidden;
}

.novel-cover-link {
    display: block;
    width: 100%;
    height: 100%;
}

.novel-cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.novel-card:hover .novel-cover-image {
    transform: scale(1.05);
}

.novel-cover-placeholder {
    width: 100%;
    height: 100%;
    background: var(--background-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: var(--font-size-2xl);
}

.novel-status {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    color: white;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-light);
}

.novel-status--ongoing {
    background: rgba(52, 168, 83, 0.9);
}

.novel-status--completed {
    background: rgba(26, 115, 232, 0.9);
}

.novel-status--hiatus {
    background: rgba(255, 193, 7, 0.9);
}

.novel-card-content {
    padding: var(--spacing-md);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.novel-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0 0 var(--spacing-sm) 0;
    line-height: var(--line-height-tight);
}

.novel-title-link {
    color: var(--text-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.novel-title-link:hover {
    color: var(--primary-color);
}

.novel-meta {
    margin-bottom: var(--spacing-md);
}

.novel-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.novel-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.rating-stars {
    display: flex;
    gap: 1px;
}

.rating-stars .star-filled {
    color: #fbbf24;
}

.rating-stars .star-empty {
    color: var(--border-color);
}

.rating-text {
    color: var(--text-secondary);
    font-weight: 500;
}

.novel-chapters {
    margin-top: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.chapter-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
}

.chapter-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(4px);
}

.chapter-number {
    font-weight: 500;
}

.chapter-time {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.chapter-link:hover .chapter-time {
    color: rgba(255, 255, 255, 0.8);
}

/* Popular Section */
.section-card {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

.section-card-header {
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-light);
}

.section-card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
}

.section-card-title i {
    color: var(--accent-color);
}

.popular-list {
    padding: var(--spacing-sm);
}

.popular-item {
    margin-bottom: var(--spacing-sm);
}

.popular-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.popular-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(4px);
    box-shadow: var(--shadow-medium);
}

.popular-rank {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--font-size-sm);
}

.popular-link:hover .popular-rank {
    background: white;
    color: var(--primary-color);
}

.popular-cover {
    flex-shrink: 0;
    width: 48px;
    height: 64px;
    border-radius: var(--radius-md);
    overflow: hidden;
}

.popular-cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popular-content {
    flex: 1;
    min-width: 0;
}

.popular-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0 0 var(--spacing-xs) 0;
    line-height: var(--line-height-tight);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.popular-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.popular-link:hover .popular-meta {
    color: rgba(255, 255, 255, 0.8);
}

.popular-views,
.popular-chapter {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Genres Section */
.genres-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.genre-card {
    position: relative;
}

.genre-link {
    display: block;
    padding: var(--spacing-lg);
    background: var(--genre-bg, var(--background-secondary));
    border: 2px solid transparent;
    border-radius: var(--radius-xl);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.genre-link:hover {
    border-color: var(--genre-color, var(--primary-color));
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    color: var(--genre-color, var(--primary-color));
}

.genre-icon {
    font-size: var(--font-size-2xl);
    color: var(--genre-color, var(--primary-color));
    margin-bottom: var(--spacing-md);
    transition: transform var(--transition-normal);
}

.genre-link:hover .genre-icon {
    transform: scale(1.2);
}

.genre-content {
    position: relative;
    z-index: 1;
}

.genre-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0 0 var(--spacing-xs) 0;
    line-height: var(--line-height-tight);
}

.genre-count {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.genre-link:hover .genre-count {
    color: var(--genre-color, var(--primary-color));
}

/* No content messages */
.no-novels-message,
.no-popular-message,
.no-genres-message {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.no-novels-message i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-muted);
}

.no-novels-message h3 {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-description {
        font-size: var(--font-size-base);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-actions .btn {
        width: 100%;
        max-width: 280px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .novels-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }

    .genres-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: var(--spacing-sm);
    }

    .genre-link {
        padding: var(--spacing-md);
    }

    .genre-icon {
        font-size: var(--font-size-xl);
    }

    .popular-link {
        padding: var(--spacing-xs);
    }

    .popular-rank {
        width: 28px;
        height: 28px;
        font-size: var(--font-size-xs);
    }

    .popular-cover {
        width: 40px;
        height: 56px;
    }
}

@media (max-width: 576px) {
    .novels-grid {
        grid-template-columns: 1fr;
    }

    .genres-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-section {
        padding: var(--spacing-xl) 0;
    }
}
</style>

<?php get_footer(); ?>