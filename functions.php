<?php
// Include the navwalker
require_once get_template_directory() . '/class-wp-bootstrap-navwalker.php';

// Include Google OAuth handler
require_once get_template_directory() . '/includes/GoogleOAuth.php';

if (!defined('ABSPATH')) {
    exit;
}

// Theme version and info
define('LN_READER_VERSION', '1.3.0');
define('LN_READER_RELEASE_DATE', '2025-07-21');

/**
 * Get theme version info
 */
function ln_reader_get_version_info() {
    return array(
        'version' => LN_READER_VERSION,
        'release_date' => LN_READER_RELEASE_DATE,
        'features' => array(
            'google_oauth' => true,
            'import_system' => true,
            'custom_auth' => true,
            'adsense_integration' => true,
            'site_kit_compatibility' => true,
            'auto_ads_support' => true,
            'responsive_design' => true,
            'novel_management' => true
        ),
        'changelog_url' => get_template_directory_uri() . '/CHANGELOG.md'
    );
}



// Register Novel post type
function register_novel_post_type() {
    $labels = array(
        'name' => 'Novels',
        'singular_name' => 'Novel',
        'add_new' => 'Add New Novel',
        'add_new_item' => 'Add New Novel',
        'edit_item' => 'Edit Novel',
        'new_item' => 'New Novel',
        'view_item' => 'View Novel',
        'search_items' => 'Search Novels',
        'not_found' => 'No novels found',
        'not_found_in_trash' => 'No novels found in trash',
        'menu_name' => 'Novels'
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'novel'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 5,
        'menu_icon'          => 'dashicons-book',
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'comments'),
        'show_in_rest'       => true
    );
    
    register_post_type('novel', $args);
    
    // Remove excerpt meta box
    remove_post_type_support('novel', 'excerpt');

    // Note: Using WordPress default rewrite handling
}
add_action('init', 'register_novel_post_type', 0);

// Note: Custom permalink functionality removed - using WordPress default permalinks

// Theme Setup
function ln_reader_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));

    // Register Menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'ln-reader'),
        'footer' => __('Footer Menu', 'ln-reader'),
    ));

    // Custom image sizes
    add_image_size('novel-cover', 200, 280, true);

    // Register Novel Tags taxonomy
    register_taxonomy('novel_tag', 'novel', array(
        'labels' => array(
            'name' => 'Novel Tags',
            'singular_name' => 'Novel Tag',
            'search_items' => 'Search Tags',
            'popular_items' => 'Popular Tags',
            'all_items' => 'All Tags',
            'parent_item' => null,
            'parent_item_colon' => null,
            'edit_item' => 'Edit Tag',
            'update_item' => 'Update Tag',
            'add_new_item' => 'Add New Tag',
            'new_item_name' => 'New Tag Name',
            'add_or_remove_items' => 'Add or Remove Tags',
            'choose_from_most_used' => 'Choose from most used tags',
            'menu_name' => 'Tags'
        ),
        'hierarchical' => false,
        'show_ui' => true,
        'show_in_rest' => true,
        'update_count_callback' => '_update_post_term_count',
        'query_var' => true,
        'rewrite' => array('slug' => 'novel-tag')
    ));

    // Register Genres taxonomy
    register_taxonomy('novel_genre', 'novel', array(
        'hierarchical' => true,
        'labels' => array(
            'name' => 'Genres',
            'singular_name' => 'Genre',
            'search_items' => 'Search Genres',
            'all_items' => 'All Genres',
            'parent_item' => 'Parent Genre',
            'parent_item_colon' => 'Parent Genre:',
            'edit_item' => 'Edit Genre',
            'update_item' => 'Update Genre',
            'add_new_item' => 'Add New Genre',
            'new_item_name' => 'New Genre Name',
            'menu_name' => 'Genres'
        ),
        'show_ui' => true,
        'show_in_rest' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'genre')
    ));

    // Register additional taxonomies for import compatibility

    // Register 'genre' taxonomy (from XML file)
    register_taxonomy('genre', array('post', 'novel', 'series'), array(
        'hierarchical' => true,
        'labels' => array(
            'name' => 'Import Genres',
            'singular_name' => 'Import Genre'
        ),
        'show_ui' => false,
        'show_in_rest' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'import-genre')
    ));

    // Register 'writer' taxonomy (from XML file)
    register_taxonomy('writer', array('post', 'novel', 'series'), array(
        'hierarchical' => false,
        'labels' => array(
            'name' => 'Writers',
            'singular_name' => 'Writer'
        ),
        'show_ui' => true,
        'show_in_rest' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'writer')
    ));

    // Register 'artist' taxonomy (from XML file)
    register_taxonomy('artist', array('post', 'novel', 'series'), array(
        'hierarchical' => false,
        'labels' => array(
            'name' => 'Artists',
            'singular_name' => 'Artist'
        ),
        'show_ui' => true,
        'show_in_rest' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'artist')
    ));

    // Register 'type' taxonomy (from XML file)
    register_taxonomy('type', array('post', 'novel', 'series'), array(
        'hierarchical' => false,
        'labels' => array(
            'name' => 'Types',
            'singular_name' => 'Type'
        ),
        'show_ui' => true,
        'show_in_rest' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'type')
    ));
}
add_action('after_setup_theme', 'ln_reader_setup');

// Enqueue scripts and styles
function ln_reader_scripts() {
    // Enqueue Bootstrap CSS
    wp_enqueue_style('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
    
    // Enqueue Bootstrap Icons CSS
    wp_enqueue_style('bootstrap-icons', 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css');

    // Enqueue Font Awesome CSS
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

    // Enqueue theme styles
    wp_enqueue_style('ln-reader-style', get_stylesheet_uri());
    wp_enqueue_style('latest-releases', get_template_directory_uri() . '/css/latest-releases.css', array(), '1.0.0');
    wp_enqueue_style('footer-style', get_template_directory_uri() . '/css/footer.css', array(), '1.0.0');
    wp_enqueue_style('chapter-buttons', get_template_directory_uri() . '/css/chapter-buttons.css', array(), '1.0.0');
    wp_enqueue_style('adsense-styles', get_template_directory_uri() . '/css/adsense-styles.css', array(), '1.0.0');
    
    // Enqueue Bootstrap JS and its dependencies
    wp_enqueue_script('bootstrap-bundle', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js', array('jquery'), '5.3.0', true);
    
    // Enqueue theme scripts
    wp_enqueue_script('ln-reader-script', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);

    // Localize script for AJAX functionality
    wp_localize_script('ln-reader-script', 'ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('ln_reader_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'ln_reader_scripts');

// Note: Custom rewrite rules removed - using WordPress default URL handling

// Note: Custom query variables removed - using WordPress defaults

// Note: Custom query handling removed - using WordPress default query processing

// Note: Custom primary category function removed - using WordPress defaults

// Note: Custom post permalink modification removed - using WordPress default permalinks

// Set post name when saving chapter
function set_chapter_post_name($data) {
    if ($data['post_type'] !== 'post' || empty($_POST['novel_id']) || empty($_POST['chapter_number'])) {
        return $data;
    }

    // If post title is manually set and not empty, respect it
    if (!empty($data['post_title']) && $data['post_title'] !== 'Auto Draft') {
        return $data;
    }

    $novel = get_post($_POST['novel_id']);
    if ($novel) {
        $volume = '';
        if (!empty($_POST['volume_number'])) {
            // Check if volume already contains "Volume" text
            if (stripos($_POST['volume_number'], 'volume') !== false) {
                $volume = ' ' . $_POST['volume_number'];
            } else {
                $volume = ' Volume ' . $_POST['volume_number'];
            }
        }
        $data['post_title'] = $novel->post_title . $volume . ' - Chapter ' . $_POST['chapter_number'];
    }

    return $data;
}
add_filter('wp_insert_post_data', 'set_chapter_post_name', 10, 1);

// Save chapter meta data
function save_chapter_meta($post_id) {
    if (!isset($_POST['chapter_details_nonce']) || 
        !wp_verify_nonce($_POST['chapter_details_nonce'], 'chapter_details_nonce')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    // Save novel ID
    if (isset($_POST['novel_id'])) {
        $novel_id = sanitize_text_field($_POST['novel_id']);
        update_post_meta($post_id, '_novel_id', $novel_id);
        
        // Get novel slug
        $novel = get_post($novel_id);
        if ($novel) {
            $novel_slug = $novel->post_name;
            
            // Save chapter number and update post name
            if (isset($_POST['chapter_number'])) {
                $chapter_number = sanitize_text_field($_POST['chapter_number']);
                update_post_meta($post_id, '_chapter_number', $chapter_number);

                // Create a clean chapter slug using post title, not chapter title
                $post = get_post($post_id);
                if ($post && !empty($post->post_title) && $post->post_title !== 'Auto Draft') {
                    // Use the actual post title for the slug
                    $clean_slug = sanitize_title($post->post_title);
                } else {
                    // Fallback: Use simple "chapter-X" format
                    $clean_slug = 'chapter-' . $chapter_number;
                }

                // Update post name with clean slug
                remove_action('save_post', 'save_chapter_meta');
                wp_update_post(array(
                    'ID' => $post_id,
                    'post_name' => $clean_slug
                ));
                add_action('save_post', 'save_chapter_meta');
            }
        }
    }
    
    // Save volume number
    if (isset($_POST['volume_number'])) {
        update_post_meta($post_id, '_volume_number', sanitize_text_field($_POST['volume_number']));
    }
    
    // Save chapter title
    if (isset($_POST['chapter_title'])) {
        update_post_meta($post_id, '_chapter_title', sanitize_text_field($_POST['chapter_title']));
    }
}
add_action('save_post', 'save_chapter_meta');

// Helper Functions
function get_time_ago($datetime) {
    return human_time_diff(strtotime($datetime), current_time('timestamp')) . ' ago';
}

function get_chapter_url($novel_id, $chapter_number) {
    // Find the chapter post to get its actual slug
    $chapters = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => 1,
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'value' => $novel_id,
                'compare' => '='
            ),
            array(
                'key' => '_chapter_number',
                'value' => $chapter_number,
                'compare' => '='
            )
        )
    ));

    if (!empty($chapters)) {
        $chapter = $chapters[0];
        // Use WordPress default permalink
        return get_permalink($chapter->ID);
    }

    return '';
}

function get_novel_url($novel_id) {
    // Use WordPress default permalink
    return get_permalink($novel_id);
}

// Note: Custom URL generation functions removed - using WordPress defaults

// Note: Custom permalink flush and fix functions removed - using WordPress defaults

// Note: Chapter slug fix function removed - using WordPress default slug handling

// Note: Custom permalink fix for imported posts removed - using WordPress defaults

// Handle category slug conflicts with existing pages/posts
function ln_reader_handle_category_conflicts($term_id, $tt_id, $taxonomy) {
    if ($taxonomy !== 'category') {
        return;
    }

    $category = get_term($term_id, 'category');
    if (!$category || is_wp_error($category)) {
        return;
    }

    // Check if there's a page or novel with the same slug
    $conflicting_page = get_page_by_path($category->slug);
    $conflicting_novel = get_page_by_path($category->slug, OBJECT, 'novel');

    if ($conflicting_page || $conflicting_novel) {
        // Add suffix to category slug to avoid conflicts
        $new_slug = $category->slug . '-category';
        $counter = 1;

        // Ensure uniqueness
        while (get_page_by_path($new_slug) || get_page_by_path($new_slug, OBJECT, 'novel') || get_term_by('slug', $new_slug, 'category')) {
            $new_slug = $category->slug . '-category-' . $counter;
            $counter++;
        }

        wp_update_term($term_id, 'category', array('slug' => $new_slug));
    }
}

add_action('created_category', 'ln_reader_handle_category_conflicts', 10, 3);
add_action('edited_category', 'ln_reader_handle_category_conflicts', 10, 3);



function trim_synopsis($content, $length = 500) {
    $content = wp_strip_all_tags($content);
    if (strlen($content) > $length) {
        $content = substr($content, 0, $length);
        $content = substr($content, 0, strrpos($content, ' '));
        $content .= '... <button class="read-more">read more</button>';
    }
    return $content;
}

function get_novel_rating($novel_id) {
    // Pastikan novel_id valid
    $novel_id = absint($novel_id);
    if (!$novel_id) {
        return array(
            'average' => 0.0,
            'count' => 0
        );
    }

    // Get meta values with strict type checking
    $total_rating = get_post_meta($novel_id, '_total_rating', true);
    $rating_count = get_post_meta($novel_id, '_rating_count', true);
    $average_rating = get_post_meta($novel_id, '_average_rating', true);

    // Set default values if empty or invalid
    if (!is_numeric($total_rating)) $total_rating = 0;
    if (!is_numeric($rating_count)) $rating_count = 0;
    if (!is_numeric($average_rating)) $average_rating = 0;

    // Convert to proper types
    $total = floatval($total_rating);
    $count = absint($rating_count);
    $average = floatval($average_rating);

    // Recalculate average if needed
    if ($count > 0 && $total > 0) {
        $average = round($total / $count, 1);
    }

    // Ensure average is between 0 and 5
    $average = max(0, min(5, $average));

    return array(
        'average' => $average,
        'count' => $count
    );
}

// Add meta boxes for novel details
function add_novel_meta_boxes() {
    add_meta_box(
        'novel_details',
        'Novel Details',
        'novel_details_callback',
        'novel',
        'normal',
        'high'
    );
    
    // Remove default custom fields meta box
    remove_meta_box('postcustom', 'novel', 'normal');
}
add_action('add_meta_boxes', 'add_novel_meta_boxes');

function novel_details_callback($post) {
    wp_nonce_field('novel_details_nonce', 'novel_details_nonce');
    
    // Get current values
    $author = get_post_meta($post->ID, '_author', true);
    $status = get_post_meta($post->ID, '_novel_status', true);
    $alternative_title = get_post_meta($post->ID, '_alternative_title', true);
    $native_language = get_post_meta($post->ID, '_native_language', true);
    $release_year = get_post_meta($post->ID, '_release_year', true);
    $novelupdates_url = get_post_meta($post->ID, '_novelupdates_url', true);
    ?>
    <div class="novel-meta-box">
        <p>
            <label for="novel_author"><strong>Author:</strong></label><br>
            <input type="text" id="novel_author" name="author" value="<?php echo esc_attr($author); ?>" class="widefat">
        </p>
        <p>
            <label for="alternative_title"><strong>Alternative Title:</strong></label><br>
            <input type="text" id="alternative_title" name="alternative_title" value="<?php echo esc_attr($alternative_title); ?>" class="widefat">
        </p>
        <p>
            <label for="native_language"><strong>Native Language:</strong></label><br>
            <input type="text" id="native_language" name="native_language" value="<?php echo esc_attr($native_language); ?>" class="widefat">
        </p>
        <p>
            <label for="release_year"><strong>Release Year:</strong></label><br>
            <input type="number" id="release_year" name="release_year" value="<?php echo esc_attr($release_year); ?>" class="widefat" min="1900" max="<?php echo date('Y'); ?>">
        </p>
        <p>
            <label for="novelupdates_url"><strong>NovelUpdates URL:</strong></label><br>
            <input type="url" id="novelupdates_url" name="novelupdates_url" value="<?php echo esc_url($novelupdates_url); ?>" class="widefat">
        </p>
        <p>
            <label for="novel_status"><strong>Status:</strong></label><br>
            <select id="novel_status" name="novel_status" class="widefat">
                <option value="ongoing" <?php selected($status, 'ongoing'); ?>>Ongoing</option>
                <option value="completed" <?php selected($status, 'completed'); ?>>Completed</option>
                <option value="hiatus" <?php selected($status, 'hiatus'); ?>>Hiatus</option>
                <option value="dropped" <?php selected($status, 'dropped'); ?>>Dropped</option>
            </select>
        </p>
    </div>
    <?php
}

// Save novel meta data
function save_novel_meta($post_id) {
    if (!isset($_POST['novel_details_nonce']) || !wp_verify_nonce($_POST['novel_details_nonce'], 'novel_details_nonce')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Save author (save to both meta keys untuk kompatibilitas)
    if (isset($_POST['author'])) {
        $author = sanitize_text_field($_POST['author']);
        update_post_meta($post_id, '_author', $author);
        update_post_meta($post_id, '_novel_author', $author);
    }

    // Save other fields
    $fields = array(
        'novel_status',
        'alternative_title',
        'native_language',
        'release_year',
        'novelupdates_url'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            $value = sanitize_text_field($_POST[$field]);
            update_post_meta($post_id, '_' . $field, $value);
        }
    }
}
add_action('save_post_novel', 'save_novel_meta');

// Add meta box for chapter details
function add_chapter_meta_boxes() {
    add_meta_box(
        'chapter_details',
        __('Chapter Details', 'lnreader'),
        'chapter_details_callback',
        'post', // Change back to post type
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_chapter_meta_boxes');

function chapter_details_callback($post) {
    wp_nonce_field('chapter_details_nonce', 'chapter_details_nonce');
    
    $novel_id = get_post_meta($post->ID, '_novel_id', true);
    $volume_number = get_post_meta($post->ID, '_volume_number', true);
    $chapter_number = get_post_meta($post->ID, '_chapter_number', true);
    $chapter_title = get_post_meta($post->ID, '_chapter_title', true);
    
    // Get all novels
    $novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC'
    ));
    ?>
    <div class="chapter-details">
        <p>
            <label for="novel_id"><?php _e('Select Novel:', 'lnreader'); ?></label><br>
            <select name="novel_id" id="novel_id" class="widefat" required>
                <option value=""><?php _e('-- Select Novel --', 'lnreader'); ?></option>
                <?php foreach ($novels as $novel) : ?>
                    <option value="<?php echo $novel->ID; ?>" <?php selected($novel_id, $novel->ID); ?>>
                        <?php echo $novel->post_title; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </p>
        <p>
            <label for="volume_number"><?php _e('Volume Title:', 'lnreader'); ?></label><br>
            <input type="text" id="volume_number" name="volume_number" class="widefat"
                value="<?php echo esc_attr($volume_number); ?>"
                placeholder="<?php esc_attr_e('e.g., 1 - The Collapse of the Mask Arc', 'lnreader'); ?>">
        </p>
        <p>
            <label for="chapter_number"><?php _e('Chapter Number:', 'lnreader'); ?></label><br>
            <input type="number" id="chapter_number" name="chapter_number" class="widefat" required min="0" step="1" 
                value="<?php echo esc_attr($chapter_number); ?>"
                placeholder="<?php esc_attr_e('Required', 'lnreader'); ?>">
        </p>
        <p>
            <label for="chapter_title"><?php _e('Chapter Title:', 'lnreader'); ?></label><br>
            <input type="text" id="chapter_title" name="chapter_title" class="widefat" 
                value="<?php echo esc_attr($chapter_title); ?>"
                placeholder="<?php esc_attr_e('Optional', 'lnreader'); ?>">
        </p>
    </div>
    <style>
    .chapter-details p {
        margin: 1em 0;
    }
    .chapter-details label {
        display: inline-block;
        margin-bottom: 0.5em;
    }
    .chapter-details input[required],
    .chapter-details select[required] {
        border-left: 4px solid #007cba;
    }
    </style>
    <?php
}

// Automatically set chapter title based on novel, volume, and chapter number
function set_chapter_title($data) {
    if ($data['post_type'] !== 'post' || empty($_POST['novel_id']) || empty($_POST['chapter_number'])) {
        return $data;
    }

    // If post title is manually set and not empty, respect it
    if (!empty($data['post_title']) && $data['post_title'] !== 'Auto Draft') {
        return $data;
    }

    $novel = get_post($_POST['novel_id']);
    if ($novel) {
        $volume = '';
        if (!empty($_POST['volume_number'])) {
            // Check if volume already contains "Volume" text
            if (stripos($_POST['volume_number'], 'volume') !== false) {
                $volume = ' ' . $_POST['volume_number'];
            } else {
                $volume = ' Volume ' . $_POST['volume_number'];
            }
        }
        $data['post_title'] = $novel->post_title . $volume . ' - Chapter ' . $_POST['chapter_number'];
    }

    return $data;
}
add_filter('wp_insert_post_data', 'set_chapter_title', 10, 1);

// Add custom columns to chapters admin list
function ln_reader_chapter_columns($columns) {
    // Create a new columns array with only the desired columns
    $new_columns = array();

    // Add checkbox for bulk actions
    if (isset($columns['cb'])) {
        $new_columns['cb'] = $columns['cb'];
    }

    // Add title column
    $new_columns['title'] = __('Title', 'lnreader');

    // Add author column
    $new_columns['author'] = __('Author', 'lnreader');

    // Add categories column
    if (isset($columns['categories'])) {
        $new_columns['categories'] = $columns['categories'];
    }

    // Add tags column
    if (isset($columns['tags'])) {
        $new_columns['tags'] = $columns['tags'];
    }

    // Add a custom icon column (the icon shown in your screenshot)
    $new_columns['status_icon'] = '';

    // Add date column
    if (isset($columns['date'])) {
        $new_columns['date'] = $columns['date'];
    }

    // Add views column
    $new_columns['views'] = __('Views', 'lnreader');

    // Add another views column (as shown in your screenshot)
    $new_columns['views2'] = __('Views', 'lnreader');

    return $new_columns;
}
add_filter('manage_post_posts_columns', 'ln_reader_chapter_columns');

// Add content to custom columns
function ln_reader_chapter_custom_column($column, $post_id) {
    switch ($column) {
        case 'status_icon':
            // Add a simple icon or status indicator
            echo '<span class="dashicons dashicons-media-text" style="color: #666;"></span>';
            break;
        case 'views':
            $views = get_post_meta($post_id, '_view_count', true);
            if (!$views) {
                $views = get_post_meta($post_id, 'post_views', true); // Alternative meta key
            }
            echo $views ? number_format($views) : '0';
            break;
        case 'views2':
            // Second views column (duplicate for layout matching)
            $views = get_post_meta($post_id, '_view_count', true);
            if (!$views) {
                $views = get_post_meta($post_id, 'post_views', true); // Alternative meta key
            }
            echo $views ? number_format($views) : '0';
            break;
    }
}
add_action('manage_post_posts_custom_column', 'ln_reader_chapter_custom_column', 10, 2);

// Make columns sortable
function ln_reader_chapter_sortable_columns($columns) {
    $columns['views'] = 'views';
    $columns['views2'] = 'views2';
    return $columns;
}
add_filter('manage_edit-post_sortable_columns', 'ln_reader_chapter_sortable_columns');

// Handle custom column sorting
function ln_reader_chapter_custom_orderby($query) {
    if (!is_admin()) {
        return;
    }

    $orderby = $query->get('orderby');

    switch ($orderby) {
        case 'views':
        case 'views2':
            $query->set('meta_key', '_view_count');
            $query->set('orderby', 'meta_value_num');
            break;
    }
}
add_action('pre_get_posts', 'ln_reader_chapter_custom_orderby');

// Enqueue admin styles for chapter management
function ln_reader_admin_styles() {
    $screen = get_current_screen();
    if ($screen && $screen->id === 'edit-post') {
        wp_enqueue_style('ln-reader-admin', get_template_directory_uri() . '/css/admin-style.css', array(), '1.0.0');
    }
}
add_action('admin_enqueue_scripts', 'ln_reader_admin_styles');

// Add AJAX actions
add_action('wp_ajax_save_novel_rating', 'save_novel_rating');

function save_novel_rating() {
    if (!is_user_logged_in()) {
        wp_send_json_error(__('Please login to rate this novel', 'lnreader'));
    }

    $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
    $rating = isset($_POST['rating']) ? intval($_POST['rating']) : 0;
    $user_id = get_current_user_id();
    
    // Check if rating is valid
    if (!$post_id || $rating < 1 || $rating > 5) {
        wp_send_json_error(__('Invalid rating', 'lnreader'));
    }

    // Check if user already rated
    $user_ratings = get_post_meta($post_id, '_user_ratings', true) ?: array();
    if (isset($user_ratings[$user_id])) {
        wp_send_json_error(__('You have already rated this novel', 'lnreader'));
    }

    // Get current rating data
    $total_rating = floatval(get_post_meta($post_id, '_total_rating', true)) ?: 0;
    $rating_count = intval(get_post_meta($post_id, '_rating_count', true)) ?: 0;

    // Update total rating and count
    $new_total = $total_rating + $rating;
    $new_count = $rating_count + 1;
    
    // Calculate average
    $average = round($new_total / $new_count, 1);

    // Save the rating
    update_post_meta($post_id, '_total_rating', $new_total);
    update_post_meta($post_id, '_rating_count', $new_count);
    update_post_meta($post_id, '_average_rating', $average);

    // Save user rating
    $user_ratings[$user_id] = $rating;
    update_post_meta($post_id, '_user_ratings', $user_ratings);

    wp_send_json_success([
        'average' => $average,
        'count' => $new_count,
        'message' => __('Rating saved successfully', 'lnreader')
    ]);
}

function has_user_rated($post_id) {
    if (!is_user_logged_in() || empty($post_id)) {
        return false;
    }
    
    $user_id = get_current_user_id();
    $user_ratings = get_post_meta($post_id, '_user_ratings', true);
    
    // Pastikan user_ratings adalah array
    if (empty($user_ratings) || !is_array($user_ratings)) {
        return false;
    }
    
    return isset($user_ratings[$user_id]) && !empty($user_ratings[$user_id]);
}

function get_user_reading_progress($novel_id) {
    if (!is_user_logged_in()) {
        return false;
    }
    
    $user_id = get_current_user_id();
    $reading_progress = get_user_meta($user_id, 'reading_progress', true);
    
    // Ensure reading_progress is an array
    if (!is_array($reading_progress)) {
        $reading_progress = array();
    }
    
    if (empty($reading_progress) || !isset($reading_progress[$novel_id])) {
        return false;
    }
    
    return $reading_progress[$novel_id];
}

function get_latest_chapter($novel_id) {
    $latest_chapter = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => 1,
        'orderby' => 'date',
        'order' => 'DESC',
        'tax_query' => array(
            array(
                'taxonomy' => 'category',
                'field' => 'name',
                'terms' => get_the_title($novel_id)
            )
        )
    ));
    
    return !empty($latest_chapter) ? $latest_chapter[0] : false;
}

function get_latest_chapters($novel_id, $limit = 3) {
    $latest_chapters = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => $limit,
        'orderby' => 'date',
        'order' => 'DESC',
        'tax_query' => array(
            array(
                'taxonomy' => 'category',
                'field' => 'name',
                'terms' => get_the_title($novel_id)
            )
        )
    ));
    
    return !empty($latest_chapters) ? $latest_chapters : false;
}

// Novel Score Functions
function get_novel_score($novel_id) {
    $score = get_post_meta($novel_id, '_novel_score', true);
    return $score ? floatval($score) : 0;
}

// Novel Views Functions
function get_novel_views($novel_id) {
    $views = get_post_meta($novel_id, '_novel_views', true);
    return $views ? intval($views) : 0;
}

function increment_novel_views($novel_id) {
    $views = get_novel_views($novel_id);
    update_post_meta($novel_id, '_novel_views', $views + 1);
}

// Time Format Functions
function time_elapsed_string($datetime) {
    $now = current_time('timestamp');
    $ago = strtotime($datetime);
    $diff = $now - $ago;

    if ($diff < 60) {
        return 'just now';
    }

    $intervals = array(
        1                => array('year', 31556926),
        $diff < 31556926 => array('month', 2629744),
        $diff < 2629744  => array('week', 604800),
        $diff < 604800   => array('day', 86400),
        $diff < 86400    => array('hour', 3600),
        $diff < 3600     => array('minute', 60),
        $diff < 60       => array('second', 1)
    );

    foreach ($intervals as $condition => $interval) {
        if ($condition) {
            $count = floor($diff / $interval[1]);
            return $count . ' ' . $interval[0] . ($count > 1 ? 's' : '') . ' ago';
        }
    }
}

function truncate_title($title, $length = 40) {
    if (mb_strlen($title) <= $length) {
        return $title;
    }
    return mb_substr($title, 0, $length) . '...';
}

// Automatically add novel title as category when creating new novel
function add_novel_title_as_category($post_id, $post, $update) {
    // Verify if this is a novel post type
    if (get_post_type($post_id) !== 'novel') {
        return;
    }

    // Skip during import to prevent duplicates
    if (defined('WP_IMPORTING') && WP_IMPORTING) {
        return;
    }

    // Skip if this is an update and novel already has categories
    if ($update && !empty(wp_get_post_categories($post_id))) {
        return;
    }

    // Skip if this novel was created during import (has import meta)
    if (get_post_meta($post_id, '_import_original_id', true)) {
        return;
    }

    // Get the novel title
    $novel_title = get_the_title($post_id);

    // Check if category exists
    $cat = get_term_by('name', $novel_title, 'category');

    if (!$cat) {
        // Create new category with novel title
        $new_cat = wp_insert_term(
            $novel_title,
            'category',
            array(
                'description' => __('Category for novel: ', 'lnreader') . $novel_title,
                'slug' => sanitize_title($novel_title)
            )
        );

        if (!is_wp_error($new_cat)) {
            // Assign the category to the novel
            wp_set_post_categories($post_id, array($new_cat['term_id']), true);
        }
    } else {
        // Assign existing category to the novel
        wp_set_post_categories($post_id, array($cat->term_id), true);
    }
}
add_action('wp_insert_post', 'add_novel_title_as_category', 10, 3);

// Customize default post type labels
function customize_post_type_labels() {
    global $wp_post_types;
    
    // Customize 'post' post type labels
    $labels = &$wp_post_types['post']->labels;
    $labels->name = __('Chapters', 'lnreader');
    $labels->singular_name = __('Chapter', 'lnreader');
    $labels->add_new = __('Add Chapter', 'lnreader');
    $labels->add_new_item = __('Add New Chapter', 'lnreader');
    $labels->edit_item = __('Edit Chapter', 'lnreader');
    $labels->new_item = __('New Chapter', 'lnreader');
    $labels->view_item = __('View Chapter', 'lnreader');
    $labels->view_items = __('View Chapters', 'lnreader');
    $labels->search_items = __('Search Chapters', 'lnreader');
    $labels->not_found = __('No chapters found', 'lnreader');
    $labels->not_found_in_trash = __('No chapters found in trash', 'lnreader');
    $labels->all_items = __('All Chapters', 'lnreader');
    $labels->archives = __('Chapter Archives', 'lnreader');
    $labels->attributes = __('Chapter Attributes', 'lnreader');
    $labels->insert_into_item = __('Insert into chapter', 'lnreader');
    $labels->uploaded_to_this_item = __('Uploaded to this chapter', 'lnreader');
    $labels->filter_items_list = __('Filter chapters list', 'lnreader');
    $labels->items_list_navigation = __('Chapters list navigation', 'lnreader');
    $labels->items_list = __('Chapters list', 'lnreader');
    $labels->menu_name = __('Chapters', 'lnreader');
    $labels->name_admin_bar = __('Chapter', 'lnreader');
    $labels->item_published = __('Chapter published', 'lnreader');
    $labels->item_published_privately = __('Chapter published privately', 'lnreader');
    $labels->item_reverted_to_draft = __('Chapter reverted to draft', 'lnreader');
    $labels->item_scheduled = __('Chapter scheduled', 'lnreader');
    $labels->item_updated = __('Chapter updated', 'lnreader');
}
add_action('init', 'customize_post_type_labels', 999);

// Change "Enter title here" placeholder for posts
function change_post_title_placeholder($title) {
    $screen = get_current_screen();
    if ('post' == $screen->post_type) {
        $title = __('Enter post title', 'lnreader');
    }
    return $title;
}
add_filter('enter_title_here', 'change_post_title_placeholder');

// Customize admin menu order
function custom_menu_order($menu_ord) {
    if (!$menu_ord) return true;
    
    return array(
        'index.php', // Dashboard
        'edit.php?post_type=novel', // Novels
        'edit.php', // Posts (now Chapter)
        'upload.php', // Media
        'edit.php?post_type=page', // Pages
        'edit-comments.php', // Comments
        'themes.php', // Appearance
        'plugins.php', // Plugins
        'users.php', // Users
        'tools.php', // Tools
        'options-general.php', // Settings
    );
}
add_filter('custom_menu_order', '__return_true');
add_filter('menu_order', 'custom_menu_order');

// Register menu pages
function ln_reader_admin_menu() {
    // Remove default Posts menu
    remove_menu_page('edit.php');
    
    // Add custom Chapters menu using default post type
    add_menu_page(
        __('Chapters', 'lnreader'),
        __('Chapters', 'lnreader'),
        'edit_posts',
        'edit.php',
        '',
        'dashicons-media-text',
        20
    );
}
add_action('admin_menu', 'ln_reader_admin_menu');

// Create Bookmarks page on theme activation
function create_bookmarks_page() {
    $bookmarks_page = get_page_by_path('bookmarks');
    
    if (!$bookmarks_page) {
        $page_data = array(
            'post_title'    => 'Bookmarks',
            'post_content'  => '',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'bookmarks'
        );
        
        $page_id = wp_insert_post($page_data);
        
        if ($page_id && !is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', 'page-bookmarks.php');
            
            // Add to primary menu
            $locations = get_theme_mod('nav_menu_locations');
            if (!empty($locations['primary'])) {
                $menu_id = $locations['primary'];
                $menu_item_data = array(
                    'menu-item-object-id' => $page_id,
                    'menu-item-object'    => 'page',
                    'menu-item-type'      => 'post_type',
                    'menu-item-status'    => 'publish',
                    'menu-item-title'     => 'Bookmarks',
                    'menu-item-url'       => get_permalink($page_id)
                );
                wp_update_nav_menu_item($menu_id, 0, $menu_item_data);
            }
        }
    }
}
add_action('after_switch_theme', 'create_bookmarks_page');

// Bookmark functions
function toggle_bookmark() {
    // Verify nonce
    if (!check_ajax_referer('bookmark_nonce', 'security', false)) {
        wp_send_json_error('Invalid security token');
        return;
    }

    if (!is_user_logged_in()) {
        wp_send_json_error('Please login to bookmark novels');
        return;
    }

    $novel_id = isset($_POST['novel_id']) ? intval($_POST['novel_id']) : 0;

    if (!$novel_id) {
        wp_send_json_error('Invalid novel ID');
        return;
    }

    $user_id = get_current_user_id();
    $bookmarked_novels = get_user_meta($user_id, 'bookmarked_novels', true);
    if (!is_array($bookmarked_novels)) {
        $bookmarked_novels = array();
    }

    $is_bookmarked = in_array($novel_id, $bookmarked_novels);

    if ($is_bookmarked) {
        $bookmarked_novels = array_diff($bookmarked_novels, array($novel_id));
        $message = 'Novel removed from bookmarks';
        $action = 'removed';
    } else {
        $bookmarked_novels[] = $novel_id;
        $message = 'Novel added to bookmarks';
        $action = 'added';
    }

    $result = update_user_meta($user_id, 'bookmarked_novels', array_values($bookmarked_novels));

    wp_send_json_success(array(
        'message' => $message,
        'action' => $action
    ));
}

add_action('wp_ajax_toggle_bookmark', 'toggle_bookmark');
add_action('wp_ajax_nopriv_toggle_bookmark', function() {
    wp_send_json_error('Please login to bookmark novels');
});

// AJAX handler for loading more novels
function load_more_novels() {
    $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $posts_per_page = 12;

    $args = array(
        'post_type' => 'novel',
        'posts_per_page' => $posts_per_page,
        'paged' => $page,
        'post_status' => 'publish'
    );

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        ob_start();
        while ($query->have_posts()) {
            $query->the_post();
            // Include novel card template or output HTML
            ?>
            <div class="col-md-4 mb-4">
                <div class="card novel-card h-100">
                    <div class="card-img-top-wrapper">
                        <?php if (has_post_thumbnail()) : ?>
                            <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail('medium', array('class' => 'card-img-top')); ?>
                            </a>
                        <?php else : ?>
                            <div class="placeholder-img">
                                <i class="fas fa-book"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h5>
                        <p class="card-text"><?php echo wp_trim_words(get_the_excerpt(), 15); ?></p>
                    </div>
                </div>
            </div>
            <?php
        }
        wp_reset_postdata();
        $html = ob_get_clean();

        wp_send_json_success(array('html' => $html));
    } else {
        wp_send_json_error('No more novels found');
    }
}
add_action('wp_ajax_load_more_novels', 'load_more_novels');
add_action('wp_ajax_nopriv_load_more_novels', 'load_more_novels');

// AJAX handler for novel search
function search_novels() {
    $query = isset($_POST['query']) ? sanitize_text_field($_POST['query']) : '';

    if (empty($query)) {
        wp_send_json_error('Empty search query');
        return;
    }

    $args = array(
        'post_type' => 'novel',
        'posts_per_page' => 10,
        's' => $query,
        'post_status' => 'publish'
    );

    $search_query = new WP_Query($args);
    $results = array();

    if ($search_query->have_posts()) {
        while ($search_query->have_posts()) {
            $search_query->the_post();
            $results[] = array(
                'title' => get_the_title(),
                'url' => get_permalink(),
                'thumbnail' => has_post_thumbnail() ? get_the_post_thumbnail_url(get_the_ID(), 'thumbnail') : get_template_directory_uri() . '/images/default-cover.jpg',
                'author' => get_post_meta(get_the_ID(), '_novel_author', true) ?: 'Unknown Author'
            );
        }
        wp_reset_postdata();
    }

    wp_send_json_success($results);
}
add_action('wp_ajax_search_novels', 'search_novels');
add_action('wp_ajax_nopriv_search_novels', 'search_novels');

// Bookmark script functionality is handled inline in single-novel.php
// No separate bookmark.js file needed since functionality is already implemented

// Reading Progress Tracking Functions
function update_reading_progress() {
    if (!check_ajax_referer('reading_progress_nonce', 'nonce', false)) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $user_id = get_current_user_id();
    $novel_id = intval($_POST['novel_id']);
    $chapter_number = intval($_POST['chapter_number']);
    
    if (!$novel_id || !$chapter_number) {
        wp_send_json_error('Invalid data');
        return;
    }

    $reading_progress = get_user_meta($user_id, 'reading_progress', true);
    if (!is_array($reading_progress)) {
        $reading_progress = array();
    }

    $reading_progress[$novel_id] = $chapter_number;
    update_user_meta($user_id, 'reading_progress', $reading_progress);

    wp_send_json_success();
}
add_action('wp_ajax_update_reading_progress', 'update_reading_progress');
add_action('wp_ajax_nopriv_update_reading_progress', 'update_reading_progress');

// Reading progress functionality is handled by:
// - Server-side automatic tracking (save_reading_progress function)
// - Visual progress bar in main.js
// - AJAX functionality in auth.js
// No separate reading-progress.js file needed

// Add custom columns to novel post type
function ln_reader_novel_columns($columns) {
    $new_columns = array();
    foreach ($columns as $key => $value) {
        if ($key === 'title') {
            $new_columns[$key] = $value;
            $new_columns['author'] = __('Author', 'lnreader');
            $new_columns['posted_by'] = __('Posted by', 'lnreader');
            $new_columns['views'] = __('Views', 'lnreader');
            $new_columns['last_chapter'] = __('Last Chapter', 'lnreader');
        } else if ($key !== 'author') { // Skip default author column
            $new_columns[$key] = $value;
        }
    }
    return $new_columns;
}
add_filter('manage_novel_posts_columns', 'ln_reader_novel_columns');

// Fill novel custom columns
function ln_reader_novel_custom_column($column, $post_id) {
    switch ($column) {
        case 'author':
            $author = get_post_meta($post_id, 'author', true);
            echo $author ? esc_html($author) : '—';
            break;
        case 'posted_by':
            $author_id = get_post_field('post_author', $post_id);
            echo get_the_author_meta('display_name', $author_id);
            break;
        case 'views':
            $views = get_post_meta($post_id, '_view_count', true);
            echo $views ? number_format($views) : '0';
            break;
        case 'last_chapter':
            $chapters = get_posts(array(
                'post_type' => 'post',
                'posts_per_page' => 1,
                'meta_query' => array(
                    array(
                        'key' => '_novel_id',
                        'value' => $post_id,
                        'compare' => '='
                    )
                ),
                'meta_key' => '_chapter_number',
                'orderby' => 'meta_value_num',
                'order' => 'DESC'
            ));
            
            if (!empty($chapters)) {
                $chapter = $chapters[0];
                $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                echo "Chapter " . $chapter_number;
            } else {
                echo '—';
            }
            break;
    }
}
add_action('manage_novel_posts_custom_column', 'ln_reader_novel_custom_column', 10, 2);

// Make novel columns sortable
function ln_reader_novel_sortable_columns($columns) {
    $columns['author'] = 'author';
    $columns['posted_by'] = 'author';
    $columns['views'] = 'views';
    $columns['last_chapter'] = 'last_chapter';
    return $columns;
}
add_filter('manage_edit-novel_sortable_columns', 'ln_reader_novel_sortable_columns');

// Handle novel columns sorting
function ln_reader_novel_custom_orderby($query) {
    if (!is_admin() || !$query->is_main_query() || $query->get('post_type') !== 'novel') {
        return;
    }

    $orderby = $query->get('orderby');
    switch ($orderby) {
        case 'author':
            $query->set('meta_key', 'author');
            $query->set('orderby', 'meta_value');
            break;
        case 'views':
            $query->set('meta_key', '_view_count');
            $query->set('orderby', 'meta_value_num');
            break;
    }
}
add_action('pre_get_posts', 'ln_reader_novel_custom_orderby');

/**
 * Get next chapter based on chapter number
 */
function get_next_chapter($current_chapter_id, $novel_id) {
    $current_chapter_number = intval(get_post_meta($current_chapter_id, '_chapter_number', true));
    
    // Query untuk mencari chapter berikutnya
    $args = array(
        'post_type' => 'post', // Ubah dari 'chapter' ke 'post'
        'posts_per_page' => 1,
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'value' => $novel_id
            ),
            array(
                'key' => '_chapter_number',
                'value' => $current_chapter_number,
                'compare' => '>',
                'type' => 'NUMERIC'
            )
        ),
        'orderby' => 'meta_value_num',
        'meta_key' => '_chapter_number',
        'order' => 'ASC'
    );

    $next_chapter = get_posts($args);
    
    if (!empty($next_chapter)) {
        return $next_chapter[0];
    }
    
    return false;
}

/**
 * Get previous chapter based on chapter number
 */
function get_prev_chapter($current_chapter_id, $novel_id) {
    $current_chapter_number = intval(get_post_meta($current_chapter_id, '_chapter_number', true));
    
    // Query untuk mencari chapter sebelumnya
    $args = array(
        'post_type' => 'post', // Ubah dari 'chapter' ke 'post'
        'posts_per_page' => 1,
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'value' => $novel_id
            ),
            array(
                'key' => '_chapter_number',
                'value' => $current_chapter_number,
                'compare' => '<',
                'type' => 'NUMERIC'
            )
        ),
        'orderby' => 'meta_value_num',
        'meta_key' => '_chapter_number',
        'order' => 'DESC'
    );

    $prev_chapter = get_posts($args);
    
    if (!empty($prev_chapter)) {
        return $prev_chapter[0];
    }
    
    return false;
}

// Save reading progress when viewing a chapter
function save_reading_progress($post_id) {
    if (!is_user_logged_in() || !is_single()) {
        return;
    }

    $novel_id = get_post_meta($post_id, '_novel_id', true);
    if (!$novel_id) {
        return;
    }

    $user_id = get_current_user_id();
    $reading_progress = get_user_meta($user_id, 'reading_progress', true);
    if (!is_array($reading_progress)) {
        $reading_progress = array();
    }

    $reading_progress[$novel_id] = $post_id;
    update_user_meta($user_id, 'reading_progress', $reading_progress);
}
add_action('wp', function() {
    if (is_single()) {
        save_reading_progress(get_the_ID());
    }
});

// Fungsi untuk menghitung views
function set_post_views() {
    // Check if we're on a single post or single novel page
    if (!is_single() && !is_singular('novel')) {
        return;
    }

    $post_id = get_the_ID();

    // Cek apakah sudah ada session untuk post ini
    if (!isset($_SESSION['post_viewed'])) {
        $_SESSION['post_viewed'] = array();
    }

    // Jika post belum dilihat dalam session ini
    if (!in_array($post_id, $_SESSION['post_viewed'])) {
        $count = get_post_meta($post_id, 'post_views', true);

        if ($count == '') {
            delete_post_meta($post_id, 'post_views');
            add_post_meta($post_id, 'post_views', '1');
        } else {
            $count++;
            update_post_meta($post_id, 'post_views', $count);
        }

        // Tandai post sudah dilihat dalam session ini
        $_SESSION['post_viewed'][] = $post_id;
    }
}

// ========================================
// WORDPRESS IMPORT COMPATIBILITY SYSTEM
// ========================================

// Register the 'series' post type for import compatibility
function register_series_post_type_for_import() {
    $labels = array(
        'name' => 'Series',
        'singular_name' => 'Series',
        'add_new' => 'Add New Series',
        'add_new_item' => 'Add New Series',
        'edit_item' => 'Edit Series',
        'new_item' => 'New Series',
        'view_item' => 'View Series',
        'search_items' => 'Search Series',
        'not_found' => 'No series found',
        'not_found_in_trash' => 'No series found in trash',
        'menu_name' => 'Series (Import)'
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => false, // Hide from admin menu since we'll convert to novels
        'query_var'          => true,
        'rewrite'            => array('slug' => 'series'),
        'capability_type'    => 'post',
        'has_archive'        => false,
        'hierarchical'       => false,
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt'),
        'show_in_rest'       => true
    );

    register_post_type('series', $args);
}
add_action('init', 'register_series_post_type_for_import', 0);

// Hook into WordPress import process
function ln_reader_import_compatibility_init() {
    // Add filters for import process
    add_filter('wp_import_post_data_processed', 'ln_reader_process_import_post_data', 10, 2);
    add_filter('wp_import_post_meta', 'ln_reader_process_import_meta', 10, 3);
    add_action('wp_import_insert_post', 'ln_reader_post_import_processing', 10, 4);
    add_action('import_end', 'ln_reader_finalize_import');

    // Add error handling
    add_action('wp_import_insert_post_failed', 'ln_reader_handle_import_error', 10, 2);
    add_filter('wp_import_existing_post', 'ln_reader_handle_existing_post', 10, 2);

    // Enable import support
    add_theme_support('post-thumbnails');
    add_theme_support('custom-header');
    add_theme_support('custom-background');
    add_theme_support('automatic-feed-links');
    add_theme_support('html5', array('comment-list', 'comment-form', 'search-form', 'gallery', 'caption'));
}
add_action('init', 'ln_reader_import_compatibility_init');

// Process imported post data and convert series to novels
function ln_reader_process_import_post_data($postdata, $post) {
    // Convert 'series' post type to 'novel'
    if (isset($postdata['post_type']) && $postdata['post_type'] === 'series') {
        $postdata['post_type'] = 'novel';

        // Check for existing novel with same title to prevent duplicates
        $existing_novel = get_page_by_title($postdata['post_title'], OBJECT, 'novel');
        if ($existing_novel) {
            // Check if existing novel has import ID
            $existing_import_id = get_post_meta($existing_novel->ID, '_import_original_id', true);
            $current_import_id = isset($post['post_id']) ? $post['post_id'] : null;

            if ($existing_import_id && $current_import_id && $existing_import_id == $current_import_id) {
                // This is the same novel being re-imported, skip it
                return false; // Skip this import
            }
        }


    }

    // Ensure proper post status
    if (!in_array($postdata['post_status'], array('publish', 'draft', 'private', 'pending'))) {
        $postdata['post_status'] = 'publish';
    }

    // Preserve original title exactly as it appears in XML
    if (isset($postdata['post_title'])) {
        // Decode HTML entities and preserve the exact title
        $postdata['post_title'] = html_entity_decode($postdata['post_title'], ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }

    return $postdata;
}

// Process and map imported meta data
function ln_reader_process_import_meta($meta, $post_id, $post) {
    $mapped_meta = array();

    foreach ($meta as $meta_item) {
        $key = $meta_item['key'];
        $value = $meta_item['value'];

        // Map meta fields from import to theme structure
        switch ($key) {
            case 'ero_chapter':
                $mapped_meta[] = array('key' => '_chapter_number', 'value' => intval($value));
                break;

            case 'ero_title':
                $mapped_meta[] = array('key' => '_chapter_title', 'value' => $value);
                break;

            case 'ero_series':
                // Store original series ID for later mapping
                $mapped_meta[] = array('key' => '_import_series_id', 'value' => $value);
                break;

            case 'ero_status':
                $mapped_meta[] = array('key' => '_novel_status', 'value' => strtolower($value));
                break;

            case 'ero_alt':
                $mapped_meta[] = array('key' => '_alternative_title', 'value' => $value);
                break;

            case 'ero_lang':
                $mapped_meta[] = array('key' => '_native_language', 'value' => $value);
                break;

            case 'ero_date':
                $mapped_meta[] = array('key' => '_release_year', 'value' => $value);
                break;

            case 'wpb_post_views_count':
                $mapped_meta[] = array('key' => 'post_views', 'value' => $value);
                $mapped_meta[] = array('key' => '_view_count', 'value' => $value);
                break;

            default:
                // Keep original meta if no mapping exists
                $mapped_meta[] = $meta_item;
                break;
        }
    }

    return $mapped_meta;
}

// Post-import processing for each imported post
function ln_reader_post_import_processing($post_id, $original_post_id, $postdata, $post) {
    // Handle series to novel conversion
    if (get_post_type($post_id) === 'novel') {
        // Set default novel meta if not present
        if (!get_post_meta($post_id, '_novel_status', true)) {
            update_post_meta($post_id, '_novel_status', 'ongoing');
        }

        // Ensure _import_original_id is set for novels
        if (!get_post_meta($post_id, '_import_original_id', true) && $original_post_id) {
            update_post_meta($post_id, '_import_original_id', $original_post_id);
        }

        // Create category for the novel if it doesn't exist
        $novel_title = get_the_title($post_id);
        $cat = get_term_by('name', $novel_title, 'category');

        if (!$cat) {
            $new_cat = wp_insert_term(
                $novel_title,
                'category',
                array(
                    'description' => __('Category for novel: ', 'lnreader') . $novel_title,
                    'slug' => sanitize_title($novel_title)
                )
            );

            if (!is_wp_error($new_cat)) {
                wp_set_post_categories($post_id, array($new_cat['term_id']), true);
            }
        } else {
            wp_set_post_categories($post_id, array($cat->term_id), true);
        }
    }

    // Handle chapter posts - immediate processing
    if (get_post_type($post_id) === 'post') {
        $import_series_id = get_post_meta($post_id, '_import_series_id', true);
        $chapter_number = get_post_meta($post_id, '_chapter_number', true);



        if ($import_series_id && $chapter_number) {
            // Try to find the novel immediately
            $novel_id = ln_reader_find_novel_by_import_id($import_series_id);

            if ($novel_id) {
                // Set novel relationship
                update_post_meta($post_id, '_novel_id', $novel_id);

                // Update post slug to match theme structure
                // Create clean chapter slug without novel name duplication
                $chapter_title = get_post_meta($post_id, '_chapter_title', true);
                if (!empty($chapter_title)) {
                    $clean_slug = sanitize_title($chapter_title);
                } else {
                    $clean_slug = 'chapter-' . $chapter_number;
                }

                wp_update_post(array(
                    'ID' => $post_id,
                    'post_name' => $clean_slug
                ));

                // Set category to match novel
                $novel_categories = wp_get_post_categories($novel_id);
                if (!empty($novel_categories)) {
                    wp_set_post_categories($post_id, $novel_categories);
                }

            }
        }
    }
}

// Finalize import process
function ln_reader_finalize_import() {

    // Clean up duplicate novels first
    ln_reader_cleanup_duplicate_novels();

    // First pass: Map series IDs to novel IDs for chapters that weren't mapped during import
    $chapters_query = new WP_Query(array(
        'post_type' => 'post',
        'meta_query' => array(
            array(
                'key' => '_import_series_id',
                'compare' => 'EXISTS'
            ),
            array(
                'key' => '_novel_id',
                'compare' => 'NOT EXISTS'
            )
        ),
        'posts_per_page' => -1
    ));

    $mapped_count = 0;
    $failed_count = 0;

    if ($chapters_query->have_posts()) {

        while ($chapters_query->have_posts()) {
            $chapters_query->the_post();
            $post_id = get_the_ID();
            $import_series_id = get_post_meta($post_id, '_import_series_id', true);
            $chapter_number = get_post_meta($post_id, '_chapter_number', true);

            if ($import_series_id) {
                // Find novel by import ID
                $novel_id = ln_reader_find_novel_by_import_id($import_series_id);

                if ($novel_id) {
                    update_post_meta($post_id, '_novel_id', $novel_id);

                    // Update post slug if we have chapter number
                    if ($chapter_number) {
                        // Create clean chapter slug using post title, not chapter title
                        $post = get_post($post_id);
                        if ($post && !empty($post->post_title) && $post->post_title !== 'Auto Draft') {
                            $clean_slug = sanitize_title($post->post_title);
                        } else {
                            $clean_slug = 'chapter-' . $chapter_number;
                        }

                        wp_update_post(array(
                            'ID' => $post_id,
                            'post_name' => $clean_slug
                        ));
                    }

                    $mapped_count++;
                } else {
                    // Try to find by category name as fallback
                    $categories = wp_get_post_categories($post_id, array('fields' => 'names'));
                    if (!empty($categories)) {
                        $category_name = $categories[0];
                        $novel_by_title = get_page_by_title($category_name, OBJECT, 'novel');
                        if ($novel_by_title) {
                            update_post_meta($post_id, '_novel_id', $novel_by_title->ID);
                            $mapped_count++;
                        } else {
                            $failed_count++;
                        }
                    } else {
                        $failed_count++;
                    }
                }
            }
        }
    }
    wp_reset_postdata();

    // Second pass: Handle genre assignments
    ln_reader_finalize_genre_assignments();

    // Clean up temporary meta fields (but keep for debugging if needed)
    // delete_post_meta_by_key('_import_series_id');
    // delete_post_meta_by_key('_import_original_id');

    // Flush rewrite rules
    flush_rewrite_rules();
}

// Finalize genre assignments after import
function ln_reader_finalize_genre_assignments() {

    $genre_assignments = 0;

    // First, handle posts that have 'genre' taxonomy terms
    $posts_with_genres = get_posts(array(
        'post_type' => array('post', 'novel'),
        'posts_per_page' => -1,
        'tax_query' => array(
            array(
                'taxonomy' => 'genre',
                'operator' => 'EXISTS'
            )
        )
    ));

    foreach ($posts_with_genres as $post) {
        $genre_terms = wp_get_post_terms($post->ID, 'genre');

        foreach ($genre_terms as $genre_term) {
            // Check if there's a corresponding novel_genre
            $novel_genre = get_term_by('name', $genre_term->name, 'novel_genre');
            if (!$novel_genre) {
                // Create novel_genre from genre
                $new_genre = wp_insert_term(
                    $genre_term->name,
                    'novel_genre',
                    array(
                        'description' => $genre_term->description ?: 'Genre: ' . $genre_term->name,
                        'slug' => $genre_term->slug
                    )
                );

                if (!is_wp_error($new_genre)) {
                    $novel_genre = get_term($new_genre['term_id'], 'novel_genre');
                }
            }

            if ($novel_genre && !is_wp_error($novel_genre)) {
                // Assign novel_genre to the post
                $result = wp_set_post_terms($post->ID, array($novel_genre->term_id), 'novel_genre', true);
                if (!is_wp_error($result)) {
                    $genre_assignments++;
                }
            }
        }
    }

    // Second, handle posts that have categories but might need novel_genre assignments
    $posts_with_categories = get_posts(array(
        'post_type' => array('post', 'novel'),
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_import_original_id',
                'compare' => 'EXISTS'
            )
        )
    ));

    foreach ($posts_with_categories as $post) {
        $categories = wp_get_post_categories($post->ID, array('fields' => 'all'));

        foreach ($categories as $category) {
            // Check if there's a corresponding novel_genre
            $novel_genre = get_term_by('name', $category->name, 'novel_genre');
            if (!$novel_genre) {
                // Create novel_genre from category
                $new_genre = wp_insert_term(
                    $category->name,
                    'novel_genre',
                    array(
                        'description' => $category->description ?: 'Genre: ' . $category->name,
                        'slug' => $category->slug . '-genre'
                    )
                );

                if (!is_wp_error($new_genre)) {
                    $novel_genre = get_term($new_genre['term_id'], 'novel_genre');
                }
            }

            if ($novel_genre && !is_wp_error($novel_genre)) {
                // Assign novel_genre to the post
                $result = wp_set_post_terms($post->ID, array($novel_genre->term_id), 'novel_genre', true);
                if (!is_wp_error($result)) {
                    $genre_assignments++;
                }
            }
        }
    }


}

// Clean up duplicate novels created during import
function ln_reader_cleanup_duplicate_novels() {

    $all_novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'post_status' => 'any'
    ));

    $novels_by_title = array();
    $duplicates_removed = 0;

    // Group novels by title
    foreach ($all_novels as $novel) {
        $title = $novel->post_title;
        if (!isset($novels_by_title[$title])) {
            $novels_by_title[$title] = array();
        }
        $novels_by_title[$title][] = $novel;
    }

    // Process duplicates
    foreach ($novels_by_title as $title => $novels) {
        if (count($novels) > 1) {

            // Sort by ID to keep the first one (usually the imported one)
            usort($novels, function($a, $b) {
                // Prefer novels with import IDs
                $a_has_import = get_post_meta($a->ID, '_import_original_id', true);
                $b_has_import = get_post_meta($b->ID, '_import_original_id', true);

                if ($a_has_import && !$b_has_import) return -1;
                if (!$a_has_import && $b_has_import) return 1;

                return $a->ID - $b->ID;
            });

            $keep_novel = array_shift($novels); // Keep the first one

            // Remove duplicates
            foreach ($novels as $duplicate) {
                // Move any chapters from duplicate to the kept novel
                $chapters = get_posts(array(
                    'post_type' => 'post',
                    'meta_query' => array(
                        array(
                            'key' => '_novel_id',
                            'value' => $duplicate->ID,
                            'compare' => '='
                        )
                    ),
                    'posts_per_page' => -1
                ));

                foreach ($chapters as $chapter) {
                    update_post_meta($chapter->ID, '_novel_id', $keep_novel->ID);
                }

                // Delete the duplicate novel
                wp_delete_post($duplicate->ID, true);
                $duplicates_removed++;
            }
        }
    }


}

// Centralized function to find or create novels with duplicate prevention
function ln_reader_find_or_create_novel($title, $import_id = null) {
    // First try to find existing novel by title
    $existing_novels = get_posts(array(
        'post_type' => 'novel',
        'title' => $title,
        'posts_per_page' => 1,
        'post_status' => 'any'
    ));

    if (!empty($existing_novels)) {
        $existing_novel = $existing_novels[0];

        // If we have an import ID, check if it matches
        if ($import_id) {
            $existing_import_id = get_post_meta($existing_novel->ID, '_import_original_id', true);
            if ($existing_import_id && $existing_import_id != $import_id) {
                // Different import ID, this might be a different novel with same title
                return $existing_novel->ID; // Still return existing to prevent duplicates
            }
        }


        return $existing_novel->ID;
    }

    // If we have an import ID, check if there's a series post that will be converted to this novel
    if ($import_id) {
        $series_posts = get_posts(array(
            'post_type' => 'series',
            'meta_query' => array(
                array(
                    'key' => '_import_original_id',
                    'value' => $import_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'post_status' => 'any'
        ));

        if (!empty($series_posts)) {
            return 'SERIES_EXISTS'; // Special return value to indicate series will be converted
        }
    }

    // No existing novel found, return null to indicate creation is needed
    return null;
}

// Handle import errors
function ln_reader_handle_import_error($post_data, $error) {
    $title = isset($post_data['post_title']) ? $post_data['post_title'] : 'Unknown';
    $type = isset($post_data['post_type']) ? $post_data['post_type'] : 'Unknown';



    // Store error for later review
    $import_errors = get_option('ln_reader_import_errors', array());
    $import_errors[] = array(
        'title' => $title,
        'type' => $type,
        'error' => $error->get_error_message(),
        'timestamp' => current_time('mysql')
    );
    update_option('ln_reader_import_errors', $import_errors);
}

// Handle existing posts during import
function ln_reader_handle_existing_post($existing_post, $post_data) {
    if ($existing_post) {
        $title = isset($post_data['post_title']) ? $post_data['post_title'] : 'Unknown';
    }
    return $existing_post;
}

// Fix import issues after import completion
function ln_reader_fix_import_issues() {

    $fixed_chapters = 0;
    $fixed_novels = 0;

    // Fix chapters without novel relationships
    $orphaned_chapters = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_chapter_number',
                'compare' => 'EXISTS'
            ),
            array(
                'key' => '_novel_id',
                'compare' => 'NOT EXISTS'
            )
        )
    ));

    foreach ($orphaned_chapters as $chapter) {
        $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
        $import_series_id = get_post_meta($chapter->ID, '_import_series_id', true);

        if ($import_series_id) {
            $novel_id = ln_reader_find_novel_by_import_id($import_series_id);
            if ($novel_id) {
                update_post_meta($chapter->ID, '_novel_id', $novel_id);
                $fixed_chapters++;
            }
        } else {
            // Try to find by category
            $categories = wp_get_post_categories($chapter->ID, array('fields' => 'names'));
            if (!empty($categories)) {
                $category_name = $categories[0];
                $novel = get_page_by_title($category_name, OBJECT, 'novel');
                if ($novel) {
                    update_post_meta($chapter->ID, '_novel_id', $novel->ID);
                    $fixed_chapters++;
                }
            }
        }
    }

    // Fix novels without proper meta
    $novels_needing_fix = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_novel_status',
                'compare' => 'NOT EXISTS'
            )
        )
    ));

    foreach ($novels_needing_fix as $novel) {
        update_post_meta($novel->ID, '_novel_status', 'ongoing');
        $fixed_novels++;
    }



    return array(
        'fixed_chapters' => $fixed_chapters,
        'fixed_novels' => $fixed_novels
    );
}

// Fix genre taxonomy assignments specifically
function ln_reader_fix_genre_taxonomy_assignments() {

    $fixed_novels = 0;
    $fixed_chapters = 0;

    // Get all posts that have 'genre' taxonomy terms but no 'novel_genre' terms
    $all_posts = get_posts(array(
        'post_type' => array('post', 'novel'),
        'posts_per_page' => -1,
        'post_status' => 'any'
    ));

    foreach ($all_posts as $post) {
        $genre_terms = wp_get_post_terms($post->ID, 'genre');
        $novel_genre_terms = wp_get_post_terms($post->ID, 'novel_genre');

        // If post has genre terms but no novel_genre terms, fix it
        if (!empty($genre_terms) && empty($novel_genre_terms)) {
            $novel_genre_ids = array();

            foreach ($genre_terms as $genre_term) {
                // Find or create corresponding novel_genre
                $novel_genre = get_term_by('name', $genre_term->name, 'novel_genre');
                if (!$novel_genre) {
                    // Create novel_genre from genre
                    $new_genre = wp_insert_term(
                        $genre_term->name,
                        'novel_genre',
                        array(
                            'description' => $genre_term->description ?: 'Genre: ' . $genre_term->name,
                            'slug' => $genre_term->slug
                        )
                    );

                    if (!is_wp_error($new_genre)) {
                        $novel_genre_ids[] = $new_genre['term_id'];
                    }
                } else {
                    $novel_genre_ids[] = $novel_genre->term_id;
                }
            }

            // Assign novel_genre terms
            if (!empty($novel_genre_ids)) {
                wp_set_post_terms($post->ID, $novel_genre_ids, 'novel_genre', false);

                if ($post->post_type === 'novel') {
                    $fixed_novels++;
                } else {
                    $fixed_chapters++;
                }


            }
        }
    }



    return array(
        'fixed_novels' => $fixed_novels,
        'fixed_chapters' => $fixed_chapters
    );
}

// Fix writer taxonomy assignments specifically
function ln_reader_fix_writer_taxonomy_assignments() {

    $fixed_novels = 0;
    $fixed_chapters = 0;

    // Get all posts that have 'writer' taxonomy terms but no author meta
    $all_posts = get_posts(array(
        'post_type' => array('post', 'novel'),
        'posts_per_page' => -1,
        'post_status' => 'any'
    ));

    foreach ($all_posts as $post) {
        $writer_terms = wp_get_post_terms($post->ID, 'writer');
        $current_author = get_post_meta($post->ID, '_author', true);

        // If post has writer terms but no author meta, fix it
        if (!empty($writer_terms) && empty($current_author)) {
            $writer_names = array();

            foreach ($writer_terms as $writer_term) {
                $writer_names[] = $writer_term->name;
            }

            // Store writer names in meta fields
            if (!empty($writer_names)) {
                $author_string = implode(', ', $writer_names);
                update_post_meta($post->ID, '_author', $author_string);
                update_post_meta($post->ID, '_novel_author', $author_string);

                if ($post->post_type === 'novel') {
                    $fixed_novels++;
                } else {
                    $fixed_chapters++;
                    // For chapters, also set chapter author
                    update_post_meta($post->ID, '_chapter_author', $author_string);
                }


            }
        }
    }



    return array(
        'fixed_novels' => $fixed_novels,
        'fixed_chapters' => $fixed_chapters
    );
}

// Add import support for attachments and media
function ln_reader_import_attachment_support() {
    // Ensure media import works correctly
    add_filter('wp_import_existing_post', 'ln_reader_check_existing_attachment', 10, 2);
    add_filter('wp_import_post_data_processed', 'ln_reader_process_attachment_data', 10, 2);
}
add_action('import_start', 'ln_reader_import_attachment_support');

// Check for existing attachments to avoid duplicates
function ln_reader_check_existing_attachment($existing_post, $post) {
    if ($post['post_type'] === 'attachment') {
        $attachment_url = isset($post['attachment_url']) ? $post['attachment_url'] : '';
        if ($attachment_url) {
            $filename = basename($attachment_url);
            $existing = get_posts(array(
                'post_type' => 'attachment',
                'meta_query' => array(
                    array(
                        'key' => '_wp_attached_file',
                        'value' => $filename,
                        'compare' => 'LIKE'
                    )
                ),
                'posts_per_page' => 1
            ));

            if (!empty($existing)) {
                return $existing[0]->ID;
            }
        }
    }

    return $existing_post;
}

// Process attachment data during import
function ln_reader_process_attachment_data($postdata, $post) {
    if ($postdata['post_type'] === 'attachment') {
        // Ensure proper attachment handling
        if (isset($post['attachment_url'])) {
            $postdata['guid'] = $post['attachment_url'];
        }
    }

    return $postdata;
}

// Add error handling and logging for import process
function ln_reader_import_error_handling() {
    // Log import errors
    add_action('wp_import_insert_post_failed', 'ln_reader_log_import_error', 10, 2);
    add_action('wp_import_insert_comment_failed', 'ln_reader_log_import_error', 10, 2);
    add_action('wp_import_insert_term_failed', 'ln_reader_log_import_error', 10, 2);
}
add_action('import_start', 'ln_reader_import_error_handling');

// Log import errors
function ln_reader_log_import_error($error, $data) {
    if (is_wp_error($error)) {
        // Error logging removed for production
    }
}

// Validate import data before processing
function ln_reader_validate_import_data($post) {
    $errors = array();

    // Check required fields
    if (empty($post['post_title'])) {
        $errors[] = 'Missing post title';
    }

    if (empty($post['post_type'])) {
        $errors[] = 'Missing post type';
    }

    // Validate post type
    $allowed_post_types = array('post', 'page', 'novel', 'series', 'attachment', 'nav_menu_item');
    if (!in_array($post['post_type'], $allowed_post_types)) {
        $errors[] = 'Invalid post type: ' . $post['post_type'];
    }

    if (!empty($errors)) {
        return false;
    }

    return true;
}

// Advanced post type mapping and conversion
function ln_reader_advanced_post_type_mapping() {
    // Hook into the import process early - use the correct hook
    add_filter('wp_import_post_data_processed', 'ln_reader_pre_process_post_data', 5, 2);
    add_action('wp_import_insert_post', 'ln_reader_post_conversion_cleanup', 20, 4);
}
add_action('import_start', 'ln_reader_advanced_post_type_mapping');

// Pre-process post data before import
function ln_reader_pre_process_post_data($postdata, $post = null) {
    // Ensure we have valid data
    if (!is_array($postdata)) {
        return $postdata;
    }

    // Store original ID for reference
    if (is_array($post) && isset($post['post_id'])) {
        if (!isset($postdata['meta_input'])) {
            $postdata['meta_input'] = array();
        }
        $postdata['meta_input']['_import_original_id'] = $post['post_id'];
    }

    // Handle series to novel conversion with enhanced mapping
    if ($postdata['post_type'] === 'series') {
        $postdata['post_type'] = 'novel';

        // Ensure novel-specific defaults
        if (empty($postdata['post_excerpt'])) {
            $postdata['post_excerpt'] = wp_trim_words($postdata['post_content'], 30);
        }

        // Set default novel status
        if (!isset($postdata['meta_input']['_novel_status'])) {
            $postdata['meta_input']['_novel_status'] = 'ongoing';
        }
    }

    return $postdata;
}

// Enhanced post conversion cleanup
function ln_reader_post_conversion_cleanup($post_id, $original_post_id, $postdata, $post) {
    $post_type = get_post_type($post_id);

    // Handle converted novels (formerly series)
    if ($post_type === 'novel') {
        ln_reader_setup_converted_novel($post_id, $original_post_id, $postdata);
    }

    // Handle chapter posts with enhanced mapping
    if ($post_type === 'post') {
        ln_reader_setup_imported_chapter($post_id, $original_post_id, $postdata);
    }
}

// Setup converted novel with all necessary meta and relationships
function ln_reader_setup_converted_novel($post_id, $original_post_id, $postdata) {
    // Ensure all novel meta fields are properly set
    $novel_meta_defaults = array(
        '_novel_status' => 'ongoing',
        '_author' => 'Unknown Author',
        '_alternative_title' => '',
        '_native_language' => 'Japanese',
        '_release_year' => date('Y'),
        '_novelupdates_url' => '',
        '_total_rating' => 0,
        '_rating_count' => 0,
        '_average_rating' => 0,
        '_novel_views' => 0
    );

    foreach ($novel_meta_defaults as $meta_key => $default_value) {
        if (!get_post_meta($post_id, $meta_key, true)) {
            update_post_meta($post_id, $meta_key, $default_value);
        }
    }

    // Create and assign novel genre taxonomy terms
    $categories = wp_get_post_categories($post_id, array('fields' => 'names'));
    if (!empty($categories)) {
        $genre_terms = array();
        foreach ($categories as $category_name) {
            // Create genre term if it doesn't exist
            $genre_term = get_term_by('name', $category_name, 'novel_genre');
            if (!$genre_term) {
                $new_genre = wp_insert_term($category_name, 'novel_genre');
                if (!is_wp_error($new_genre)) {
                    $genre_terms[] = $new_genre['term_id'];
                }
            } else {
                $genre_terms[] = $genre_term->term_id;
            }
        }

        if (!empty($genre_terms)) {
            wp_set_post_terms($post_id, $genre_terms, 'novel_genre');
        }
    }

    // Set featured image if available
    $thumbnail_id = get_post_meta($post_id, '_thumbnail_id', true);
    if ($thumbnail_id) {
        set_post_thumbnail($post_id, $thumbnail_id);
    }

    error_log("LN Reader: Successfully converted series to novel - ID: $post_id, Title: " . get_the_title($post_id));
}

// Setup imported chapter with proper novel relationships
function ln_reader_setup_imported_chapter($post_id, $original_post_id, $postdata) {
    $chapter_number = get_post_meta($post_id, '_chapter_number', true);
    $import_series_id = get_post_meta($post_id, '_import_series_id', true);

    if (!$chapter_number || !$import_series_id) {
        return; // Not a valid chapter
    }

    // Find the corresponding novel
    $novel_id = ln_reader_find_novel_by_import_id($import_series_id);

    if ($novel_id) {
        // Set novel relationship
        update_post_meta($post_id, '_novel_id', $novel_id);

        // Update post slug to match theme structure
        // Create clean chapter slug using post title, not chapter title
        $post = get_post($post_id);
        if ($post && !empty($post->post_title) && $post->post_title !== 'Auto Draft') {
            $new_slug = sanitize_title($post->post_title);
        } else {
            $new_slug = 'chapter-' . $chapter_number;
        }

        wp_update_post(array(
            'ID' => $post_id,
            'post_name' => $new_slug
        ));

        // Set category to match novel
        $novel_categories = wp_get_post_categories($novel_id);
        if (!empty($novel_categories)) {
            wp_set_post_categories($post_id, $novel_categories);
        }

        // Volume number is now optional and can be text-based
        // No default value needed

        error_log("LN Reader: Successfully mapped chapter to novel - Chapter ID: $post_id, Novel ID: $novel_id, Chapter: $chapter_number");
    } else {
        error_log("LN Reader: Could not find novel for chapter - Chapter ID: $post_id, Series ID: $import_series_id");
    }
}

// Find novel by import series ID
function ln_reader_find_novel_by_import_id($import_series_id) {
    // First try by import original ID
    $novel_query = new WP_Query(array(
        'post_type' => 'novel',
        'meta_query' => array(
            array(
                'key' => '_import_original_id',
                'value' => $import_series_id,
                'compare' => '='
            )
        ),
        'posts_per_page' => 1
    ));

    if ($novel_query->have_posts()) {
        $novel_id = $novel_query->posts[0]->ID;
        wp_reset_postdata();
        error_log("LN Reader Import: Found novel $novel_id by _import_original_id '$import_series_id'");
        return $novel_id;
    }

    // Second try: Look for novels that might have been imported but not properly linked
    // Try to find by any novel that has the same original post ID
    global $wpdb;
    $novel_id = $wpdb->get_var($wpdb->prepare("
        SELECT p.ID
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'novel'
        AND pm.meta_key = '_import_original_id'
        AND pm.meta_value = %s
        LIMIT 1
    ", $import_series_id));

    if ($novel_id) {
        error_log("LN Reader Import: Found novel $novel_id by direct DB query for series ID '$import_series_id'");
        return intval($novel_id);
    }

    wp_reset_postdata();
    error_log("LN Reader Import: Could not find novel for series ID '$import_series_id'");
    return false;
}

// Comprehensive meta field mapping system
function ln_reader_meta_field_mapping_system() {
    // Hook into meta import process
    add_filter('wp_import_post_meta', 'ln_reader_comprehensive_meta_mapping', 10, 3);
    add_action('wp_import_insert_post', 'ln_reader_post_meta_cleanup', 30, 4);
}
add_action('import_start', 'ln_reader_meta_field_mapping_system');

// Comprehensive meta field mapping
function ln_reader_comprehensive_meta_mapping($meta, $post_id, $post) {
    $mapped_meta = array();
    $post_type = isset($post['post_type']) ? $post['post_type'] : 'post';

    error_log("LN Reader: Processing meta for post $post_id, type: $post_type, meta count: " . count($meta));

    foreach ($meta as $meta_item) {
        $key = $meta_item['key'];
        $value = $meta_item['value'];

        // Apply mapping based on post type and meta key
        $mapped_fields = ln_reader_get_meta_field_mapping($key, $value, $post_type);

        if (!empty($mapped_fields)) {
            foreach ($mapped_fields as $mapped_field) {
                $mapped_meta[] = $mapped_field;
                error_log("LN Reader: Mapped $key -> {$mapped_field['key']} for post $post_id");
            }
        } else {
            // Keep original meta if no mapping exists
            $mapped_meta[] = $meta_item;
        }
    }

    error_log("LN Reader: Mapped meta count for post $post_id: " . count($mapped_meta));
    return $mapped_meta;
}

// Get meta field mapping based on key, value, and post type
function ln_reader_get_meta_field_mapping($key, $value, $post_type) {
    $mapped_fields = array();

    // Common mappings for all post types
    $common_mappings = array(
        'wpb_post_views_count' => array(
            array('key' => 'post_views', 'value' => $value),
            array('key' => '_view_count', 'value' => $value)
        ),
        'wp_statistics_words_count' => array(
            array('key' => '_word_count', 'value' => $value)
        ),
        '_edit_last' => array(
            array('key' => '_edit_last', 'value' => $value)
        ),
        '_thumbnail_id' => array(
            array('key' => '_thumbnail_id', 'value' => $value)
        )
    );

    // Series/Novel specific mappings
    $novel_mappings = array(
        'ero_status' => array(
            array('key' => '_novel_status', 'value' => ln_reader_normalize_status($value))
        ),
        'ero_alt' => array(
            array('key' => '_alternative_title', 'value' => $value)
        ),
        'ero_lang' => array(
            array('key' => '_native_language', 'value' => $value)
        ),
        'ero_date' => array(
            array('key' => '_release_year', 'value' => $value)
        ),
        'ero_mature' => array(
            array('key' => '_mature_content', 'value' => $value)
        ),
        'ero_hot' => array(
            array('key' => '_featured', 'value' => $value)
        ),
        'ero_project' => array(
            array('key' => '_project_status', 'value' => $value)
        ),
        'ero_latestid' => array(
            array('key' => '_latest_chapters', 'value' => $value)
        ),
        'ero_autogenerateimgcat' => array(
            array('key' => '_auto_generate_images', 'value' => $value)
        )
    );

    // Chapter/Post specific mappings
    $chapter_mappings = array(
        'ero_chapter' => array(
            array('key' => '_chapter_number', 'value' => ln_reader_normalize_chapter_number($value))
        ),
        'ero_title' => array(
            array('key' => '_chapter_title', 'value' => $value)
        ),
        'ero_series' => array(
            array('key' => '_import_series_id', 'value' => $value)
        ),
        'ero_volume' => array(
            array('key' => '_volume_number', 'value' => $value)
        ),
        // Additional chapter number variations
        'chapter_number' => array(
            array('key' => '_chapter_number', 'value' => ln_reader_normalize_chapter_number($value))
        ),
        'chapter_num' => array(
            array('key' => '_chapter_number', 'value' => ln_reader_normalize_chapter_number($value))
        ),
        'episode_number' => array(
            array('key' => '_chapter_number', 'value' => ln_reader_normalize_chapter_number($value))
        )
    );

    // Statistics and tracking mappings
    $stats_mappings = array(
        'ts_today_view_count' => array(
            array('key' => '_today_views', 'value' => intval($value))
        ),
        'ts_monthly_view_count' => array(
            array('key' => '_monthly_views', 'value' => intval($value))
        ),
        'ts_weekly_view_count' => array(
            array('key' => '_weekly_views', 'value' => intval($value))
        ),
        'ts_monthly_view_data' => array(
            array('key' => '_monthly_view_data', 'value' => $value)
        ),
        'ts_last_view_update' => array(
            array('key' => '_last_view_update', 'value' => $value)
        )
    );

    // Plugin-specific mappings to preserve
    $plugin_mappings = array(
        '_heateor_sc_meta' => array(
            array('key' => '_heateor_sc_meta', 'value' => $value)
        ),
        'kofi_display_floating_button' => array(
            array('key' => 'kofi_display_floating_button', 'value' => $value)
        ),
        'toolstipssynonyms' => array(
            array('key' => 'toolstipssynonyms', 'value' => $value)
        ),
        'toolstipbulletscreentag' => array(
            array('key' => 'toolstipbulletscreentag', 'value' => $value)
        )
    );

    // Combine all mappings
    $all_mappings = array_merge($common_mappings, $novel_mappings, $chapter_mappings, $stats_mappings, $plugin_mappings);

    // Apply mapping if exists
    if (isset($all_mappings[$key])) {
        $mapped_fields = $all_mappings[$key];
    }

    return $mapped_fields;
}

// Normalize chapter number values
function ln_reader_normalize_chapter_number($value) {
    // Handle various chapter number formats
    if (is_numeric($value)) {
        return floatval($value);
    }

    // Try to extract number from string
    if (is_string($value)) {
        // Remove common prefixes/suffixes
        $cleaned = preg_replace('/^(chapter|ch|episode|ep)\s*/i', '', trim($value));
        $cleaned = preg_replace('/\s*(chapter|ch|episode|ep)$/i', '', $cleaned);

        // Extract number (including decimals)
        if (preg_match('/(\d+(?:\.\d+)?)/', $cleaned, $matches)) {
            return floatval($matches[1]);
        }
    }

    // Fallback to 0 if no valid number found
    error_log("LN Reader: Could not normalize chapter number from value: '$value'");
    return 0;
}

// Normalize status values
function ln_reader_normalize_status($status) {
    $status = strtolower(trim($status));

    $status_mappings = array(
        'ongoing' => 'ongoing',
        'completed' => 'completed',
        'complete' => 'completed',
        'finished' => 'completed',
        'hiatus' => 'hiatus',
        'on hold' => 'hiatus',
        'dropped' => 'dropped',
        'cancelled' => 'dropped',
        'canceled' => 'dropped'
    );

    return isset($status_mappings[$status]) ? $status_mappings[$status] : 'ongoing';
}

// Post-import meta cleanup and validation
function ln_reader_post_meta_cleanup($post_id, $original_post_id, $postdata, $post) {
    $post_type = get_post_type($post_id);

    // Clean up and validate novel meta
    if ($post_type === 'novel') {
        ln_reader_validate_novel_meta($post_id);
    }

    // Clean up and validate chapter meta
    if ($post_type === 'post') {
        ln_reader_validate_chapter_meta($post_id);
    }

    // Remove temporary import meta
    delete_post_meta($post_id, '_import_temp');
}

// Validate and fix novel meta fields
function ln_reader_validate_novel_meta($post_id) {
    // Ensure required novel meta exists
    $required_meta = array(
        '_novel_status' => 'ongoing',
        '_author' => 'Unknown Author',
        '_alternative_title' => '',
        '_native_language' => 'Japanese',
        '_release_year' => date('Y'),
        '_total_rating' => 0,
        '_rating_count' => 0,
        '_average_rating' => 0
    );

    foreach ($required_meta as $meta_key => $default_value) {
        $current_value = get_post_meta($post_id, $meta_key, true);
        if (empty($current_value) && $current_value !== '0') {
            update_post_meta($post_id, $meta_key, $default_value);
        }
    }

    // Validate numeric fields
    $numeric_fields = array('_release_year', '_total_rating', '_rating_count', '_average_rating');
    foreach ($numeric_fields as $field) {
        $value = get_post_meta($post_id, $field, true);
        if (!is_numeric($value)) {
            update_post_meta($post_id, $field, 0);
        }
    }

    // Validate status field
    $status = get_post_meta($post_id, '_novel_status', true);
    $valid_statuses = array('ongoing', 'completed', 'hiatus', 'dropped');
    if (!in_array($status, $valid_statuses)) {
        update_post_meta($post_id, '_novel_status', 'ongoing');
    }
}

// Validate and fix chapter meta fields
function ln_reader_validate_chapter_meta($post_id) {
    $chapter_number = get_post_meta($post_id, '_chapter_number', true);
    $novel_id = get_post_meta($post_id, '_novel_id', true);

    // Ensure chapter number is numeric
    if (!empty($chapter_number) && !is_numeric($chapter_number)) {
        // Try to extract number from string
        preg_match('/\d+/', $chapter_number, $matches);
        if (!empty($matches)) {
            update_post_meta($post_id, '_chapter_number', intval($matches[0]));
        } else {
            delete_post_meta($post_id, '_chapter_number');
        }
    }

    // Volume number can now be text (e.g., "1 - The Collapse of the Mask Arc")
    // No validation needed as we want to preserve the full descriptive text

    // Validate novel relationship
    if (!empty($novel_id) && !get_post($novel_id)) {
        delete_post_meta($post_id, '_novel_id');
        error_log("LN Reader: Invalid novel ID $novel_id for chapter $post_id");
    }
}

// Enhanced taxonomy and category handling for import
function ln_reader_taxonomy_import_handling() {
    // Hook into taxonomy import process
    add_filter('wp_import_term_meta', 'ln_reader_process_term_meta', 10, 3);
    add_action('wp_import_insert_term', 'ln_reader_post_term_import', 10, 4);
    add_action('wp_import_set_post_terms', 'ln_reader_enhance_post_terms', 10, 5);
}
add_action('import_start', 'ln_reader_taxonomy_import_handling');

// Process imported term meta data
function ln_reader_process_term_meta($meta, $term_id, $term) {
    // Keep all term meta as-is for now
    return $meta;
}

// Post-process imported terms
function ln_reader_post_term_import($term_id, $original_term_id, $term_data, $term) {
    $taxonomy = $term['taxonomy'];

    // Handle category to novel_genre mapping
    if ($taxonomy === 'category') {
        ln_reader_create_corresponding_genre($term_id, $term_data);
    }

    // Handle tag to novel_tag mapping
    if ($taxonomy === 'post_tag') {
        ln_reader_create_corresponding_novel_tag($term_id, $term_data);
    }

    // Handle 'genre' taxonomy to 'novel_genre' mapping
    if ($taxonomy === 'genre') {
        ln_reader_map_genre_to_novel_genre($term_id, $term_data);
    }

    // Log successful import
    error_log("LN Reader: Successfully imported {$taxonomy} term: " . ($term_data['name'] ?? 'Unknown'));
}

// Map 'genre' taxonomy to 'novel_genre'
function ln_reader_map_genre_to_novel_genre($genre_term_id, $term_data) {
    $genre_term = get_term($genre_term_id, 'genre');
    if (!$genre_term || is_wp_error($genre_term)) {
        return;
    }

    // Check if novel_genre already exists
    $existing_novel_genre = get_term_by('name', $genre_term->name, 'novel_genre');
    if ($existing_novel_genre) {
        // Store mapping for later reference
        update_term_meta($genre_term_id, '_mapped_novel_genre_id', $existing_novel_genre->term_id);
        return;
    }

    // Create corresponding novel_genre
    $novel_genre_args = array(
        'description' => $genre_term->description ?: 'Genre: ' . $genre_term->name,
        'slug' => $genre_term->slug
    );

    $new_novel_genre = wp_insert_term($genre_term->name, 'novel_genre', $novel_genre_args);

    if (!is_wp_error($new_novel_genre)) {
        // Store mapping for later reference
        update_term_meta($genre_term_id, '_mapped_novel_genre_id', $new_novel_genre['term_id']);
        update_term_meta($new_novel_genre['term_id'], '_source_genre_id', $genre_term_id);

        error_log("LN Reader: Created novel_genre '{$genre_term->name}' from genre");
    } else {
        error_log("LN Reader: Failed to create novel_genre from genre '{$genre_term->name}': " . $new_novel_genre->get_error_message());
    }
}

// Create corresponding genre term for categories
function ln_reader_create_corresponding_genre($category_id, $term_data) {
    $category = get_term($category_id, 'category');
    if (!$category || is_wp_error($category)) {
        return;
    }

    // Check if genre already exists
    $existing_genre = get_term_by('name', $category->name, 'novel_genre');
    if ($existing_genre) {
        return; // Genre already exists
    }

    // Create corresponding genre
    $genre_args = array(
        'description' => $category->description ?: 'Genre: ' . $category->name,
        'slug' => $category->slug . '-genre'
    );

    $new_genre = wp_insert_term($category->name, 'novel_genre', $genre_args);

    if (!is_wp_error($new_genre)) {
        // Store mapping for later reference
        update_term_meta($category_id, '_mapped_genre_id', $new_genre['term_id']);
        update_term_meta($new_genre['term_id'], '_source_category_id', $category_id);

        error_log("LN Reader: Created genre '{$category->name}' from category");
    }
}

// Create corresponding novel tag for post tags
function ln_reader_create_corresponding_novel_tag($tag_id, $term_data) {
    $tag = get_term($tag_id, 'post_tag');
    if (!$tag || is_wp_error($tag)) {
        return;
    }

    // Check if novel tag already exists
    $existing_novel_tag = get_term_by('name', $tag->name, 'novel_tag');
    if ($existing_novel_tag) {
        return; // Novel tag already exists
    }

    // Create corresponding novel tag
    $novel_tag_args = array(
        'description' => $tag->description ?: 'Tag: ' . $tag->name,
        'slug' => $tag->slug
    );

    $new_novel_tag = wp_insert_term($tag->name, 'novel_tag', $novel_tag_args);

    if (!is_wp_error($new_novel_tag)) {
        // Store mapping for later reference
        update_term_meta($tag_id, '_mapped_novel_tag_id', $new_novel_tag['term_id']);
        update_term_meta($new_novel_tag['term_id'], '_source_tag_id', $tag_id);

        error_log("LN Reader: Created novel tag '{$tag->name}' from post tag");
    }
}

// Enhance post terms assignment during import
function ln_reader_enhance_post_terms($tt_ids, $terms, $taxonomy, $post_id, $post) {
    $post_type = get_post_type($post_id);

    // Handle novel post type term assignments
    if ($post_type === 'novel') {
        ln_reader_assign_novel_terms($post_id, $terms, $taxonomy);
    }

    // Handle chapter post type term assignments
    if ($post_type === 'post') {
        ln_reader_assign_chapter_terms($post_id, $terms, $taxonomy);
    }
}

// Assign appropriate terms to novels
function ln_reader_assign_novel_terms($post_id, $terms, $taxonomy) {
    error_log("LN Reader: Assigning terms for novel $post_id - Taxonomy: $taxonomy, Terms: " . implode(',', $terms));

    if ($taxonomy === 'category') {
        // Map categories to novel genres
        $genre_terms = array();

        foreach ($terms as $term_id) {
            $category = get_term($term_id, 'category');
            if ($category && !is_wp_error($category)) {
                // Find or create corresponding genre
                $genre = get_term_by('name', $category->name, 'novel_genre');
                if (!$genre) {
                    // Create genre if it doesn't exist
                    $new_genre = wp_insert_term($category->name, 'novel_genre', array(
                        'description' => 'Genre: ' . $category->name,
                        'slug' => $category->slug . '-genre'
                    ));

                    if (!is_wp_error($new_genre)) {
                        $genre_terms[] = $new_genre['term_id'];
                        error_log("LN Reader: Created novel_genre '{$category->name}' from category for novel $post_id");
                    }
                } else {
                    $genre_terms[] = $genre->term_id;
                    error_log("LN Reader: Found existing novel_genre '{$category->name}' for novel $post_id");
                }
            }
        }

        // Assign genres to novel
        if (!empty($genre_terms)) {
            wp_set_post_terms($post_id, $genre_terms, 'novel_genre', true);
            error_log("LN Reader: Assigned " . count($genre_terms) . " novel_genre terms from categories to novel $post_id");
        }
    }

    // Handle 'genre' taxonomy from XML import (domain="genre")
    if ($taxonomy === 'genre') {
        $novel_genre_terms = array();

        foreach ($terms as $term_id) {
            $genre_term = get_term($term_id, 'genre');
            if ($genre_term && !is_wp_error($genre_term)) {
                // Find or create corresponding novel_genre
                $novel_genre = get_term_by('name', $genre_term->name, 'novel_genre');
                if (!$novel_genre) {
                    // Create novel_genre if it doesn't exist
                    $new_novel_genre = wp_insert_term($genre_term->name, 'novel_genre', array(
                        'description' => $genre_term->description ?: 'Genre: ' . $genre_term->name,
                        'slug' => $genre_term->slug
                    ));

                    if (!is_wp_error($new_novel_genre)) {
                        $novel_genre_terms[] = $new_novel_genre['term_id'];
                        error_log("LN Reader: Created novel_genre '{$genre_term->name}' from genre taxonomy for novel $post_id");
                    }
                } else {
                    $novel_genre_terms[] = $novel_genre->term_id;
                    error_log("LN Reader: Found existing novel_genre '{$genre_term->name}' for novel $post_id");
                }
            }
        }

        // Assign novel genres
        if (!empty($novel_genre_terms)) {
            wp_set_post_terms($post_id, $novel_genre_terms, 'novel_genre', true);
            error_log("LN Reader: Assigned " . count($novel_genre_terms) . " novel_genre terms from genre taxonomy to novel $post_id");
        }
    }

    if ($taxonomy === 'post_tag') {
        // Map post tags to novel tags
        $novel_tag_terms = array();

        foreach ($terms as $term_id) {
            $tag = get_term($term_id, 'post_tag');
            if ($tag && !is_wp_error($tag)) {
                // Find or create corresponding novel tag
                $novel_tag = get_term_by('name', $tag->name, 'novel_tag');
                if (!$novel_tag) {
                    // Create novel tag if it doesn't exist
                    $new_novel_tag = wp_insert_term($tag->name, 'novel_tag', array(
                        'description' => 'Tag: ' . $tag->name,
                        'slug' => $tag->slug
                    ));

                    if (!is_wp_error($new_novel_tag)) {
                        $novel_tag_terms[] = $new_novel_tag['term_id'];
                        error_log("LN Reader: Created novel_tag '{$tag->name}' from post_tag for novel $post_id");
                    }
                } else {
                    $novel_tag_terms[] = $novel_tag->term_id;
                }
            }
        }

        // Assign novel tags to novel
        if (!empty($novel_tag_terms)) {
            wp_set_post_terms($post_id, $novel_tag_terms, 'novel_tag', true);
            error_log("LN Reader: Assigned " . count($novel_tag_terms) . " novel_tag terms to novel $post_id");
        }
    }

    // Handle 'writer' taxonomy from XML import
    if ($taxonomy === 'writer') {
        $writer_names = array();

        foreach ($terms as $term_id) {
            $writer_term = get_term($term_id, 'writer');
            if ($writer_term && !is_wp_error($writer_term)) {
                $writer_names[] = $writer_term->name;
                error_log("LN Reader: Found writer '{$writer_term->name}' for novel $post_id");
            }
        }

        // Store writer names in meta fields
        if (!empty($writer_names)) {
            $author_string = implode(', ', $writer_names);
            update_post_meta($post_id, '_author', $author_string);
            update_post_meta($post_id, '_novel_author', $author_string);
            error_log("LN Reader: Set author meta '$author_string' for novel $post_id");
        }
    }

    // Handle 'artist' taxonomy from XML import
    if ($taxonomy === 'artist') {
        $artist_names = array();

        foreach ($terms as $term_id) {
            $artist_term = get_term($term_id, 'artist');
            if ($artist_term && !is_wp_error($artist_term)) {
                $artist_names[] = $artist_term->name;
                error_log("LN Reader: Found artist '{$artist_term->name}' for novel $post_id");
            }
        }

        // Store artist names in meta field
        if (!empty($artist_names)) {
            $artist_string = implode(', ', $artist_names);
            update_post_meta($post_id, '_artist', $artist_string);
            error_log("LN Reader: Set artist meta '$artist_string' for novel $post_id");
        }
    }

    // Handle 'type' taxonomy from XML import
    if ($taxonomy === 'type') {
        $type_names = array();

        foreach ($terms as $term_id) {
            $type_term = get_term($term_id, 'type');
            if ($type_term && !is_wp_error($type_term)) {
                $type_names[] = $type_term->name;
                error_log("LN Reader: Found type '{$type_term->name}' for novel $post_id");
            }
        }

        // Store type names in meta field
        if (!empty($type_names)) {
            $type_string = implode(', ', $type_names);
            update_post_meta($post_id, '_novel_type', $type_string);
            error_log("LN Reader: Set type meta '$type_string' for novel $post_id");
        }
    }
}

// Assign appropriate terms to chapters
function ln_reader_assign_chapter_terms($post_id, $terms, $taxonomy) {
    // For chapters, keep original category assignments
    // This helps maintain the novel-chapter relationship through categories
    error_log("LN Reader: Assigning terms for chapter $post_id - Taxonomy: $taxonomy, Terms: " . implode(',', $terms));

    if ($taxonomy === 'category') {
        // Check if any of the assigned categories is "Uncategorized"
        $has_uncategorized = false;
        $valid_categories = array();

        foreach ($terms as $term_id) {
            $category = get_term($term_id, 'category');
            if ($category && !is_wp_error($category)) {
                if ($category->slug === 'uncategorized') {
                    $has_uncategorized = true;
                    error_log("LN Reader: Chapter $post_id is being assigned to 'Uncategorized' category");
                } else {
                    $valid_categories[] = $term_id;
                }
            }
        }

        // If chapter is being assigned to "Uncategorized", try to determine the correct category
        if ($has_uncategorized && empty($valid_categories)) {
            $chapter_title = get_the_title($post_id);
            $chapter_number = get_post_meta($post_id, '_chapter_number', true);

            error_log("LN Reader: Attempting to fix uncategorized chapter $post_id: '$chapter_title'");

            // Try to determine the correct novel/category from title
            $novel_info = ln_reader_determine_novel_from_title($chapter_title);
            if ($novel_info) {
                // Find or create the correct category
                $correct_category = get_term_by('name', $novel_info['title'], 'category');
                if (!$correct_category) {
                    $category_result = wp_insert_term(
                        $novel_info['title'],
                        'category',
                        array(
                            'description' => 'Category for novel: ' . $novel_info['title'],
                            'slug' => sanitize_title($novel_info['title'])
                        )
                    );

                    if (!is_wp_error($category_result)) {
                        $correct_category = get_term($category_result['term_id'], 'category');
                        error_log("LN Reader: Created category '{$novel_info['title']}' for chapter $post_id");
                    }
                }

                if ($correct_category && !is_wp_error($correct_category)) {
                    $valid_categories = array($correct_category->term_id);
                    error_log("LN Reader: Assigned chapter $post_id to correct category '{$correct_category->name}' instead of Uncategorized");
                }
            }

            // If we still don't have a valid category, try to extract from series meta
            if (empty($valid_categories)) {
                $import_series_id = get_post_meta($post_id, '_import_series_id', true);
                if ($import_series_id) {
                    // Look for existing novels with this series ID
                    $novels = get_posts(array(
                        'post_type' => 'novel',
                        'meta_query' => array(
                            array(
                                'key' => '_original_post_id',
                                'value' => $import_series_id,
                                'compare' => '='
                            )
                        ),
                        'posts_per_page' => 1
                    ));

                    if (!empty($novels)) {
                        $novel = $novels[0];
                        $novel_categories = wp_get_post_categories($novel->ID);
                        if (!empty($novel_categories)) {
                            $valid_categories = $novel_categories;
                            error_log("LN Reader: Found novel {$novel->ID} for series ID $import_series_id, using its categories");
                        }
                    }
                }
            }
        }

        // Use valid categories or keep original terms if no uncategorized issue
        $final_categories = !empty($valid_categories) ? $valid_categories : $terms;

        // Ensure chapter is assigned to its novel's category if novel relationship exists
        $novel_id = get_post_meta($post_id, '_novel_id', true);
        if ($novel_id) {
            $novel_categories = wp_get_post_categories($novel_id);
            if (!empty($novel_categories)) {
                // Merge with existing terms
                $final_categories = array_unique(array_merge($final_categories, $novel_categories));
                error_log("LN Reader: Merged chapter categories with novel $novel_id categories");
            }
        }

        // Apply the final category assignments
        if (!empty($final_categories)) {
            wp_set_post_categories($post_id, $final_categories);
            error_log("LN Reader: Final category assignment for chapter $post_id: " . implode(',', $final_categories));
        }
    }

    // Handle 'genre' taxonomy from XML import - chapters inherit genres from their novels
    if ($taxonomy === 'genre') {
        $novel_id = get_post_meta($post_id, '_novel_id', true);
        if ($novel_id) {
            // Get the novel's genres and assign them to the chapter
            $novel_genres = wp_get_post_terms($novel_id, 'novel_genre', array('fields' => 'ids'));
            if (!empty($novel_genres)) {
                wp_set_post_terms($post_id, $novel_genres, 'novel_genre', true);
                error_log("LN Reader: Inherited " . count($novel_genres) . " novel_genre terms from novel $novel_id to chapter $post_id");
            }
        }
    }

    // Handle 'writer' taxonomy from XML import - chapters inherit author info from their novels
    if ($taxonomy === 'writer') {
        $novel_id = get_post_meta($post_id, '_novel_id', true);
        if ($novel_id) {
            // Get the novel's author and copy to chapter
            $novel_author = get_post_meta($novel_id, '_author', true);
            if ($novel_author) {
                update_post_meta($post_id, '_chapter_author', $novel_author);
                error_log("LN Reader: Inherited author '$novel_author' from novel $novel_id to chapter $post_id");
            }
        }
    }
}

// Clean up taxonomy assignments after import
function ln_reader_cleanup_taxonomy_assignments() {
    // Find all novels and ensure they have proper genre assignments
    $novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'post_status' => 'any'
    ));

    foreach ($novels as $novel) {
        $categories = wp_get_post_categories($novel->ID, array('fields' => 'names'));
        $current_genres = wp_get_post_terms($novel->ID, 'novel_genre', array('fields' => 'names'));

        // If novel has categories but no genres, create genres from categories
        if (!empty($categories) && empty($current_genres)) {
            $genre_ids = array();

            foreach ($categories as $category_name) {
                $genre = get_term_by('name', $category_name, 'novel_genre');
                if (!$genre) {
                    $new_genre = wp_insert_term($category_name, 'novel_genre');
                    if (!is_wp_error($new_genre)) {
                        $genre_ids[] = $new_genre['term_id'];
                    }
                } else {
                    $genre_ids[] = $genre->term_id;
                }
            }

            if (!empty($genre_ids)) {
                wp_set_post_terms($novel->ID, $genre_ids, 'novel_genre');
            }
        }
    }

    error_log('LN Reader: Taxonomy cleanup completed');
}

// Enhanced media and attachment import support
function ln_reader_media_import_support() {
    // Hook into attachment import process
    add_filter('wp_import_post_data_processed', 'ln_reader_process_attachment_import', 15, 2);
    add_action('wp_import_insert_post', 'ln_reader_post_attachment_processing', 25, 4);
    add_filter('wp_import_existing_post', 'ln_reader_handle_existing_attachments', 10, 2);
    add_action('import_end', 'ln_reader_finalize_media_import');
}
add_action('import_start', 'ln_reader_media_import_support');

// Increase upload limits for import
function ln_reader_increase_upload_limits() {
    // Increase upload limits for import process
    @ini_set('upload_max_filesize', '128M');
    @ini_set('post_max_size', '128M');
    @ini_set('max_execution_time', 600);
    @ini_set('memory_limit', '512M');

    // Add filter to increase WordPress upload limit
    add_filter('upload_size_limit', function($limit) {
        return 128 * 1024 * 1024; // 128MB
    });

    // Increase import file size limit
    add_filter('import_upload_size_limit', function($limit) {
        return 128 * 1024 * 1024; // 128MB
    });
}
add_action('import_start', 'ln_reader_increase_upload_limits');

// Always increase upload limits for admin users
function ln_reader_admin_upload_limits() {
    if (current_user_can('manage_options')) {
        @ini_set('upload_max_filesize', '128M');
        @ini_set('post_max_size', '128M');
        @ini_set('max_execution_time', 600);
        @ini_set('memory_limit', '512M');

        // WordPress upload filters
        add_filter('upload_size_limit', function($limit) {
            return 128 * 1024 * 1024; // 128MB
        });

        add_filter('wp_max_upload_size', function($limit) {
            return 128 * 1024 * 1024; // 128MB
        });
    }
}
add_action('admin_init', 'ln_reader_admin_upload_limits');

// Process attachment data during import
function ln_reader_process_attachment_import($postdata, $post) {
    if ($postdata['post_type'] !== 'attachment') {
        return $postdata;
    }

    // Ensure proper attachment handling
    if (isset($post['attachment_url'])) {
        $attachment_url = $post['attachment_url'];
        $postdata['guid'] = $attachment_url;

        // Extract filename and set proper post name
        $filename = basename(parse_url($attachment_url, PHP_URL_PATH));
        $postdata['post_name'] = sanitize_title(pathinfo($filename, PATHINFO_FILENAME));

        // Set proper mime type if available
        if (isset($post['post_mime_type'])) {
            $postdata['post_mime_type'] = $post['post_mime_type'];
        } else {
            // Guess mime type from file extension
            $postdata['post_mime_type'] = ln_reader_guess_mime_type($filename);
        }
    }

    return $postdata;
}

// Guess mime type from filename
function ln_reader_guess_mime_type($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

    $mime_types = array(
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'svg' => 'image/svg+xml',
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'txt' => 'text/plain',
        'zip' => 'application/zip',
        'mp3' => 'audio/mpeg',
        'mp4' => 'video/mp4',
        'avi' => 'video/x-msvideo'
    );

    return isset($mime_types[$extension]) ? $mime_types[$extension] : 'application/octet-stream';
}

// Handle existing attachments to avoid duplicates
function ln_reader_handle_existing_attachments($existing_post, $post) {
    if ($post['post_type'] !== 'attachment') {
        return $existing_post;
    }

    $attachment_url = isset($post['attachment_url']) ? $post['attachment_url'] : '';
    if (!$attachment_url) {
        return $existing_post;
    }

    $filename = basename(parse_url($attachment_url, PHP_URL_PATH));

    // Check for existing attachment by filename
    $existing_attachments = get_posts(array(
        'post_type' => 'attachment',
        'meta_query' => array(
            array(
                'key' => '_wp_attached_file',
                'value' => $filename,
                'compare' => 'LIKE'
            )
        ),
        'posts_per_page' => 1
    ));

    if (!empty($existing_attachments)) {
        error_log("LN Reader: Found existing attachment for $filename, using existing ID: " . $existing_attachments[0]->ID);
        return $existing_attachments[0]->ID;
    }

    // Check by post title/name
    $existing_by_title = get_posts(array(
        'post_type' => 'attachment',
        'name' => sanitize_title(pathinfo($filename, PATHINFO_FILENAME)),
        'posts_per_page' => 1
    ));

    if (!empty($existing_by_title)) {
        error_log("LN Reader: Found existing attachment by title for $filename, using existing ID: " . $existing_by_title[0]->ID);
        return $existing_by_title[0]->ID;
    }

    return $existing_post;
}

// Post-process attachments after import
function ln_reader_post_attachment_processing($post_id, $original_post_id, $postdata, $post) {
    if (get_post_type($post_id) !== 'attachment') {
        return;
    }

    $attachment_url = isset($post['attachment_url']) ? $post['attachment_url'] : '';
    if (!$attachment_url) {
        return;
    }

    // Try to download and process the attachment
    $downloaded = ln_reader_download_attachment($post_id, $attachment_url, $post);

    if ($downloaded) {
        // Update attachment metadata
        ln_reader_update_attachment_metadata($post_id, $post);

        // Set as featured image if this attachment is referenced as thumbnail
        ln_reader_set_featured_images($post_id, $original_post_id);
    }
}

// Download attachment from URL
function ln_reader_download_attachment($post_id, $attachment_url, $post_data) {
    // Skip if file already exists locally
    $attached_file = get_post_meta($post_id, '_wp_attached_file', true);
    if ($attached_file) {
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['basedir'] . '/' . $attached_file;
        if (file_exists($file_path)) {
            return true; // File already exists
        }
    }

    // Download the file
    $filename = basename(parse_url($attachment_url, PHP_URL_PATH));
    $upload_dir = wp_upload_dir();

    // Create year/month directory structure
    $subdir = '/' . date('Y') . '/' . date('m');
    $upload_path = $upload_dir['basedir'] . $subdir;
    $upload_url = $upload_dir['baseurl'] . $subdir;

    if (!file_exists($upload_path)) {
        wp_mkdir_p($upload_path);
    }

    $file_path = $upload_path . '/' . $filename;
    $file_url = $upload_url . '/' . $filename;

    // Download file
    $response = wp_remote_get($attachment_url, array(
        'timeout' => 30,
        'user-agent' => 'WordPress/' . get_bloginfo('version') . '; ' . get_bloginfo('url')
    ));

    if (is_wp_error($response)) {
        error_log("LN Reader: Failed to download attachment $attachment_url: " . $response->get_error_message());
        return false;
    }

    $body = wp_remote_retrieve_body($response);
    if (empty($body)) {
        error_log("LN Reader: Empty response body for attachment $attachment_url");
        return false;
    }

    // Save file
    $saved = file_put_contents($file_path, $body);
    if ($saved === false) {
        error_log("LN Reader: Failed to save attachment to $file_path");
        return false;
    }

    // Update attachment meta
    $relative_path = $subdir . '/' . $filename;
    update_post_meta($post_id, '_wp_attached_file', ltrim($relative_path, '/'));

    // Update post GUID
    wp_update_post(array(
        'ID' => $post_id,
        'guid' => $file_url
    ));

    error_log("LN Reader: Successfully downloaded attachment $filename to $file_path");
    return true;
}

// Update attachment metadata
function ln_reader_update_attachment_metadata($post_id, $post_data) {
    $attached_file = get_post_meta($post_id, '_wp_attached_file', true);
    if (!$attached_file) {
        return;
    }

    $upload_dir = wp_upload_dir();
    $file_path = $upload_dir['basedir'] . '/' . $attached_file;

    if (!file_exists($file_path)) {
        return;
    }

    // Generate attachment metadata
    $attachment_metadata = wp_generate_attachment_metadata($post_id, $file_path);
    if ($attachment_metadata) {
        wp_update_attachment_metadata($post_id, $attachment_metadata);
    }

    // Set proper mime type
    $mime_type = wp_check_filetype($file_path);
    if ($mime_type['type']) {
        wp_update_post(array(
            'ID' => $post_id,
            'post_mime_type' => $mime_type['type']
        ));
    }
}

// Set featured images for posts that reference attachments
function ln_reader_set_featured_images($attachment_id, $original_attachment_id) {
    // Find posts that reference this attachment as thumbnail
    $posts_with_thumbnail = get_posts(array(
        'post_type' => array('post', 'novel', 'page'),
        'meta_query' => array(
            array(
                'key' => '_thumbnail_id',
                'value' => $original_attachment_id,
                'compare' => '='
            )
        ),
        'posts_per_page' => -1
    ));

    foreach ($posts_with_thumbnail as $post) {
        set_post_thumbnail($post->ID, $attachment_id);
        error_log("LN Reader: Set featured image for post {$post->ID} using attachment {$attachment_id}");
    }
}

// Finalize media import
function ln_reader_finalize_media_import() {
    // Update any remaining thumbnail references
    ln_reader_update_thumbnail_references();

    // Clean up orphaned attachments
    ln_reader_cleanup_orphaned_attachments();

    error_log('LN Reader: Media import finalization completed');
}

// Update thumbnail references after import
function ln_reader_update_thumbnail_references() {
    global $wpdb;

    // Get all posts with thumbnail meta
    $posts_with_thumbnails = $wpdb->get_results(
        "SELECT post_id, meta_value FROM {$wpdb->postmeta} WHERE meta_key = '_thumbnail_id'"
    );

    foreach ($posts_with_thumbnails as $thumbnail_meta) {
        $post_id = $thumbnail_meta->post_id;
        $thumbnail_id = $thumbnail_meta->meta_value;

        // Check if thumbnail attachment exists
        $attachment = get_post($thumbnail_id);
        if (!$attachment || $attachment->post_type !== 'attachment') {
            // Try to find attachment by original ID or filename
            $found_attachment = ln_reader_find_replacement_attachment($thumbnail_id);
            if ($found_attachment) {
                update_post_meta($post_id, '_thumbnail_id', $found_attachment);
                error_log("LN Reader: Updated thumbnail reference for post $post_id from $thumbnail_id to $found_attachment");
            } else {
                delete_post_meta($post_id, '_thumbnail_id');
                error_log("LN Reader: Removed invalid thumbnail reference for post $post_id");
            }
        }
    }
}

// Find replacement attachment
function ln_reader_find_replacement_attachment($original_id) {
    // This is a placeholder - in a real scenario, you might have a mapping
    // of original IDs to new IDs stored during import
    return false;
}

// Clean up orphaned attachments
function ln_reader_cleanup_orphaned_attachments() {
    // Find attachments that failed to download
    $failed_attachments = get_posts(array(
        'post_type' => 'attachment',
        'meta_query' => array(
            array(
                'key' => '_wp_attached_file',
                'compare' => 'NOT EXISTS'
            )
        ),
        'posts_per_page' => -1
    ));

    foreach ($failed_attachments as $attachment) {
        // Check if the attachment file actually exists
        $upload_dir = wp_upload_dir();
        $guid = $attachment->guid;

        if ($guid && strpos($guid, $upload_dir['baseurl']) === false) {
            // This is an external URL that wasn't downloaded
            error_log("LN Reader: Keeping external attachment reference: " . $attachment->post_title);
        }
    }
}

// Comprehensive import error handling and logging system
function ln_reader_import_error_logging_system() {
    // Initialize logging
    add_action('import_start', 'ln_reader_init_import_logging');
    add_action('import_end', 'ln_reader_finalize_import_logging');

    // Error handling hooks
    add_action('wp_import_insert_post_failed', 'ln_reader_log_post_import_error', 10, 2);
    add_action('wp_import_insert_comment_failed', 'ln_reader_log_comment_import_error', 10, 2);
    add_action('wp_import_insert_term_failed', 'ln_reader_log_term_import_error', 10, 2);
    add_action('wp_import_insert_user_failed', 'ln_reader_log_user_import_error', 10, 2);

    // Success tracking
    add_action('wp_import_insert_post', 'ln_reader_log_post_import_success', 5, 4);
    add_action('wp_import_insert_term', 'ln_reader_log_term_import_success', 5, 4);

    // Progress tracking
    add_filter('wp_import_post_data_processed', 'ln_reader_track_import_progress', 5, 2);
}
add_action('init', 'ln_reader_import_error_logging_system');

// Initialize import logging
function ln_reader_init_import_logging() {
    // Create import log entry
    $import_log = array(
        'start_time' => current_time('mysql'),
        'status' => 'in_progress',
        'posts_processed' => 0,
        'posts_success' => 0,
        'posts_failed' => 0,
        'terms_processed' => 0,
        'terms_success' => 0,
        'terms_failed' => 0,
        'attachments_processed' => 0,
        'attachments_success' => 0,
        'attachments_failed' => 0,
        'errors' => array(),
        'warnings' => array()
    );

    update_option('ln_reader_import_log', $import_log);

    // Clear previous error log
    if (function_exists('error_clear_last')) {
        error_clear_last();
    }

    error_log('LN Reader Import: Starting import process at ' . current_time('mysql'));
}

// Track import progress
function ln_reader_track_import_progress($postdata, $post) {
    $import_log = get_option('ln_reader_import_log', array());

    if (isset($postdata['post_type'])) {
        $import_log['posts_processed'] = ($import_log['posts_processed'] ?? 0) + 1;

        // Track by post type
        $post_type = $postdata['post_type'];
        $type_key = $post_type . '_processed';
        $import_log[$type_key] = ($import_log[$type_key] ?? 0) + 1;
    }

    update_option('ln_reader_import_log', $import_log);

    return $postdata;
}

// Log successful post import
function ln_reader_log_post_import_success($post_id, $original_post_id, $postdata, $post) {
    $import_log = get_option('ln_reader_import_log', array());
    $import_log['posts_success'] = ($import_log['posts_success'] ?? 0) + 1;

    $post_type = get_post_type($post_id);
    $type_key = $post_type . '_success';
    $import_log[$type_key] = ($import_log[$type_key] ?? 0) + 1;

    // Log specific success details
    $success_entry = array(
        'type' => 'post_success',
        'post_id' => $post_id,
        'original_id' => $original_post_id,
        'post_type' => $post_type,
        'title' => get_the_title($post_id),
        'timestamp' => current_time('mysql')
    );

    if (!isset($import_log['success_details'])) {
        $import_log['success_details'] = array();
    }
    $import_log['success_details'][] = $success_entry;

    update_option('ln_reader_import_log', $import_log);
}

// Log successful term import
function ln_reader_log_term_import_success($term_id, $original_term_id, $term_data, $term) {
    $import_log = get_option('ln_reader_import_log', array());
    $import_log['terms_success'] = ($import_log['terms_success'] ?? 0) + 1;

    update_option('ln_reader_import_log', $import_log);
}

// Log post import errors
function ln_reader_log_post_import_error($error, $post_data) {
    $import_log = get_option('ln_reader_import_log', array());
    $import_log['posts_failed'] = ($import_log['posts_failed'] ?? 0) + 1;

    $error_entry = array(
        'type' => 'post_error',
        'error_message' => is_wp_error($error) ? $error->get_error_message() : 'Unknown error',
        'error_code' => is_wp_error($error) ? $error->get_error_code() : 'unknown',
        'post_data' => array(
            'title' => $post_data['post_title'] ?? 'Unknown',
            'type' => $post_data['post_type'] ?? 'Unknown',
            'status' => $post_data['post_status'] ?? 'Unknown'
        ),
        'timestamp' => current_time('mysql')
    );

    if (!isset($import_log['errors'])) {
        $import_log['errors'] = array();
    }
    $import_log['errors'][] = $error_entry;

    update_option('ln_reader_import_log', $import_log);

    error_log('LN Reader Import Error (Post): ' . $error_entry['error_message'] . ' - ' . $post_data['post_title']);
}

// Log comment import errors
function ln_reader_log_comment_import_error($error, $comment_data) {
    $import_log = get_option('ln_reader_import_log', array());

    $error_entry = array(
        'type' => 'comment_error',
        'error_message' => is_wp_error($error) ? $error->get_error_message() : 'Unknown error',
        'error_code' => is_wp_error($error) ? $error->get_error_code() : 'unknown',
        'comment_data' => array(
            'author' => $comment_data['comment_author'] ?? 'Unknown',
            'content' => wp_trim_words($comment_data['comment_content'] ?? '', 10)
        ),
        'timestamp' => current_time('mysql')
    );

    if (!isset($import_log['errors'])) {
        $import_log['errors'] = array();
    }
    $import_log['errors'][] = $error_entry;

    update_option('ln_reader_import_log', $import_log);

    error_log('LN Reader Import Error (Comment): ' . $error_entry['error_message']);
}

// Log term import errors
function ln_reader_log_term_import_error($error, $term_data) {
    $import_log = get_option('ln_reader_import_log', array());
    $import_log['terms_failed'] = ($import_log['terms_failed'] ?? 0) + 1;

    $error_entry = array(
        'type' => 'term_error',
        'error_message' => is_wp_error($error) ? $error->get_error_message() : 'Unknown error',
        'error_code' => is_wp_error($error) ? $error->get_error_code() : 'unknown',
        'term_data' => array(
            'name' => $term_data['name'] ?? 'Unknown',
            'taxonomy' => $term_data['taxonomy'] ?? 'Unknown'
        ),
        'timestamp' => current_time('mysql')
    );

    if (!isset($import_log['errors'])) {
        $import_log['errors'] = array();
    }
    $import_log['errors'][] = $error_entry;

    update_option('ln_reader_import_log', $import_log);

    error_log('LN Reader Import Error (Term): ' . $error_entry['error_message'] . ' - ' . $term_data['name']);
}

// Log user import errors
function ln_reader_log_user_import_error($error, $user_data) {
    $import_log = get_option('ln_reader_import_log', array());

    $error_entry = array(
        'type' => 'user_error',
        'error_message' => is_wp_error($error) ? $error->get_error_message() : 'Unknown error',
        'error_code' => is_wp_error($error) ? $error->get_error_code() : 'unknown',
        'user_data' => array(
            'login' => $user_data['user_login'] ?? 'Unknown',
            'email' => $user_data['user_email'] ?? 'Unknown'
        ),
        'timestamp' => current_time('mysql')
    );

    if (!isset($import_log['errors'])) {
        $import_log['errors'] = array();
    }
    $import_log['errors'][] = $error_entry;

    update_option('ln_reader_import_log', $import_log);

    error_log('LN Reader Import Error (User): ' . $error_entry['error_message'] . ' - ' . $user_data['user_login']);
}

// Add warning logging function
function ln_reader_log_import_warning($message, $context = array()) {
    $import_log = get_option('ln_reader_import_log', array());

    $warning_entry = array(
        'message' => $message,
        'context' => $context,
        'timestamp' => current_time('mysql')
    );

    if (!isset($import_log['warnings'])) {
        $import_log['warnings'] = array();
    }
    $import_log['warnings'][] = $warning_entry;

    update_option('ln_reader_import_log', $import_log);

    error_log('LN Reader Import Warning: ' . $message);
}

// Finalize import logging and generate report
function ln_reader_finalize_import_logging() {
    $import_log = get_option('ln_reader_import_log', array());

    $import_log['end_time'] = current_time('mysql');
    $import_log['status'] = 'completed';

    // Calculate duration
    if (isset($import_log['start_time'])) {
        $start = strtotime($import_log['start_time']);
        $end = strtotime($import_log['end_time']);
        $import_log['duration_seconds'] = $end - $start;
        $import_log['duration_formatted'] = ln_reader_format_duration($end - $start);
    }

    // Calculate success rates
    $posts_total = $import_log['posts_processed'] ?? 0;
    $posts_success = $import_log['posts_success'] ?? 0;
    $import_log['posts_success_rate'] = $posts_total > 0 ? round(($posts_success / $posts_total) * 100, 2) : 0;

    $terms_total = $import_log['terms_processed'] ?? 0;
    $terms_success = $import_log['terms_success'] ?? 0;
    $import_log['terms_success_rate'] = $terms_total > 0 ? round(($terms_success / $terms_total) * 100, 2) : 0;

    update_option('ln_reader_import_log', $import_log);

    // Generate and save import report
    $report = ln_reader_generate_import_report($import_log);
    update_option('ln_reader_import_report', $report);

    // Log final summary
    error_log('LN Reader Import: Completed at ' . $import_log['end_time']);
    error_log('LN Reader Import Summary: ' . $posts_success . '/' . $posts_total . ' posts imported successfully');

    if (!empty($import_log['errors'])) {
        error_log('LN Reader Import: ' . count($import_log['errors']) . ' errors occurred during import');
    }

    if (!empty($import_log['warnings'])) {
        error_log('LN Reader Import: ' . count($import_log['warnings']) . ' warnings occurred during import');
    }
}

// Format duration in human-readable format
function ln_reader_format_duration($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $seconds = $seconds % 60;

    $parts = array();
    if ($hours > 0) $parts[] = $hours . 'h';
    if ($minutes > 0) $parts[] = $minutes . 'm';
    if ($seconds > 0) $parts[] = $seconds . 's';

    return implode(' ', $parts);
}

// Generate comprehensive import report
function ln_reader_generate_import_report($import_log) {
    $report = array(
        'summary' => array(
            'start_time' => $import_log['start_time'] ?? 'Unknown',
            'end_time' => $import_log['end_time'] ?? 'Unknown',
            'duration' => $import_log['duration_formatted'] ?? 'Unknown',
            'status' => $import_log['status'] ?? 'Unknown'
        ),
        'statistics' => array(
            'posts' => array(
                'processed' => $import_log['posts_processed'] ?? 0,
                'success' => $import_log['posts_success'] ?? 0,
                'failed' => $import_log['posts_failed'] ?? 0,
                'success_rate' => $import_log['posts_success_rate'] ?? 0
            ),
            'terms' => array(
                'processed' => $import_log['terms_processed'] ?? 0,
                'success' => $import_log['terms_success'] ?? 0,
                'failed' => $import_log['terms_failed'] ?? 0,
                'success_rate' => $import_log['terms_success_rate'] ?? 0
            )
        ),
        'errors' => $import_log['errors'] ?? array(),
        'warnings' => $import_log['warnings'] ?? array(),
        'post_type_breakdown' => ln_reader_get_post_type_breakdown($import_log)
    );

    return $report;
}

// Get post type breakdown from import log
function ln_reader_get_post_type_breakdown($import_log) {
    $breakdown = array();
    $post_types = array('post', 'novel', 'series', 'attachment', 'page');

    foreach ($post_types as $post_type) {
        $processed_key = $post_type . '_processed';
        $success_key = $post_type . '_success';

        if (isset($import_log[$processed_key]) && $import_log[$processed_key] > 0) {
            $breakdown[$post_type] = array(
                'processed' => $import_log[$processed_key],
                'success' => $import_log[$success_key] ?? 0,
                'failed' => $import_log[$processed_key] - ($import_log[$success_key] ?? 0)
            );
        }
    }

    return $breakdown;
}

// Import recovery and rollback functions
function ln_reader_import_recovery_system() {
    // Add recovery hooks
    add_action('wp_import_insert_post_failed', 'ln_reader_attempt_post_recovery', 20, 2);
}
add_action('init', 'ln_reader_import_recovery_system');

// Attempt to recover failed post imports
function ln_reader_attempt_post_recovery($error, $post_data) {
    if (!is_wp_error($error)) {
        return;
    }

    $error_code = $error->get_error_code();

    // Attempt recovery based on error type
    switch ($error_code) {
        case 'empty_content':
            // Try with minimal content
            $post_data['post_content'] = 'Content imported from backup.';
            $recovered_id = wp_insert_post($post_data, true);
            if (!is_wp_error($recovered_id)) {
                ln_reader_log_import_warning('Recovered post with minimal content: ' . $post_data['post_title']);
                return $recovered_id;
            }
            break;

        case 'invalid_post_type':
            // Try with default post type
            $post_data['post_type'] = 'post';
            $recovered_id = wp_insert_post($post_data, true);
            if (!is_wp_error($recovered_id)) {
                ln_reader_log_import_warning('Recovered post with default post type: ' . $post_data['post_title']);
                return $recovered_id;
            }
            break;

        case 'invalid_date':
            // Try with current date
            $post_data['post_date'] = current_time('mysql');
            $post_data['post_date_gmt'] = current_time('mysql', 1);
            $recovered_id = wp_insert_post($post_data, true);
            if (!is_wp_error($recovered_id)) {
                ln_reader_log_import_warning('Recovered post with current date: ' . $post_data['post_title']);
                return $recovered_id;
            }
            break;
    }

    return false;
}

// Display import notices in admin
function ln_reader_display_import_notices() {
    $import_log = get_option('ln_reader_import_log');
    if (!$import_log || $import_log['status'] !== 'completed') {
        return;
    }

    // Check if user has dismissed the notice
    $user_id = get_current_user_id();
    $dismissed_key = 'ln_reader_import_notice_dismissed_' . md5(serialize($import_log));

    if (get_user_meta($user_id, $dismissed_key, true)) {
        return; // Notice has been dismissed
    }

    $errors_count = count($import_log['errors'] ?? array());
    $warnings_count = count($import_log['warnings'] ?? array());

    if ($errors_count > 0 || $warnings_count > 0) {
        echo '<div class="notice notice-warning is-dismissible" data-dismiss-key="' . esc_attr($dismissed_key) . '">';
        echo '<p><strong>LN Reader Import Report:</strong></p>';

        if ($errors_count > 0) {
            echo '<p>⚠️ ' . $errors_count . ' errors occurred during import. <a href="' . admin_url('admin.php?page=ln-reader-import-log') . '">View details</a></p>';
        }

        if ($warnings_count > 0) {
            echo '<p>⚠️ ' . $warnings_count . ' warnings occurred during import. <a href="' . admin_url('admin.php?page=ln-reader-import-log') . '">View details</a></p>';
        }

        echo '<p><small><a href="#" onclick="lnReaderDismissImportNotice(\'' . esc_js($dismissed_key) . '\'); return false;">Dismiss this notice</a> | <a href="#" onclick="lnReaderClearImportLog(); return false;">Clear import log</a></small></p>';
        echo '</div>';
    } else {
        echo '<div class="notice notice-success is-dismissible" data-dismiss-key="' . esc_attr($dismissed_key) . '">';
        echo '<p><strong>✅ LN Reader Import completed successfully!</strong></p>';
        echo '<p>All content was imported without errors.</p>';
        echo '<p><small><a href="#" onclick="lnReaderDismissImportNotice(\'' . esc_js($dismissed_key) . '\'); return false;">Dismiss this notice</a></small></p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'ln_reader_display_import_notices');

// AJAX handler for dismissing import notice
function ln_reader_dismiss_import_notice() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }

    $dismiss_key = sanitize_text_field($_POST['dismiss_key'] ?? '');
    if (empty($dismiss_key)) {
        wp_die('Invalid dismiss key');
    }

    $user_id = get_current_user_id();
    update_user_meta($user_id, $dismiss_key, true);

    wp_send_json_success('Notice dismissed');
}
add_action('wp_ajax_ln_reader_dismiss_import_notice', 'ln_reader_dismiss_import_notice');

// AJAX handler for clearing import log
function ln_reader_clear_import_log() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }

    delete_option('ln_reader_import_log');
    delete_option('ln_reader_import_report');

    wp_send_json_success('Import log cleared');
}
add_action('wp_ajax_ln_reader_clear_import_log', 'ln_reader_clear_import_log');

// Add JavaScript for handling notice dismissal
function ln_reader_import_notice_script() {
    if (!is_admin()) {
        return;
    }
    ?>
    <script type="text/javascript">
    function lnReaderDismissImportNotice(dismissKey) {
        jQuery.post(ajaxurl, {
            action: 'ln_reader_dismiss_import_notice',
            dismiss_key: dismissKey
        }, function(response) {
            if (response.success) {
                jQuery('[data-dismiss-key="' + dismissKey + '"]').fadeOut();
            }
        });
    }

    function lnReaderClearImportLog() {
        if (confirm('Are you sure you want to clear the import log? This action cannot be undone.')) {
            jQuery.post(ajaxurl, {
                action: 'ln_reader_clear_import_log'
            }, function(response) {
                if (response.success) {
                    location.reload();
                }
            });
        }
    }
    </script>
    <?php
}
add_action('admin_footer', 'ln_reader_import_notice_script');

// Add theme version info to admin dashboard
function ln_reader_admin_dashboard_version() {
    $screen = get_current_screen();
    if ($screen && $screen->id === 'dashboard') {
        $version_info = ln_reader_get_version_info();
        ?>
        <div class="wrap">
            <div class="card" style="margin-top: 20px;">
                <h3>LN Reader Theme Information</h3>
                <table class="widefat">
                    <tr>
                        <td><strong>Version:</strong></td>
                        <td><?php echo $version_info['version']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Release Date:</strong></td>
                        <td><?php echo $version_info['release_date']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Google OAuth:</strong></td>
                        <td>
                            <?php if (ln_reader_is_google_oauth_enabled()) : ?>
                                <span style="color: #28a745;">✓ Configured</span>
                            <?php else : ?>
                                <span style="color: #dc3545;">✗ Not Configured</span>
                                <a href="<?php echo admin_url('options-general.php?page=ln-reader-google-oauth'); ?>" style="margin-left: 10px;">Configure</a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Features:</strong></td>
                        <td>
                            <?php foreach ($version_info['features'] as $feature => $enabled) : ?>
                                <span style="color: <?php echo $enabled ? '#28a745' : '#dc3545'; ?>;">
                                    <?php echo $enabled ? '✓' : '✗'; ?> <?php echo ucwords(str_replace('_', ' ', $feature)); ?>
                                </span><br>
                            <?php endforeach; ?>
                        </td>
                    </tr>
                </table>
                <p><a href="<?php echo get_template_directory_uri(); ?>/CHANGELOG.md" target="_blank">View Changelog</a></p>
            </div>
        </div>
        <?php
    }
}
add_action('admin_notices', 'ln_reader_admin_dashboard_version');

// Add admin page for viewing import logs
function ln_reader_add_import_log_admin_page() {
    add_submenu_page(
        'tools.php',
        'LN Reader Import Log',
        'Import Log',
        'manage_options',
        'ln-reader-import-log',
        'ln_reader_import_log_page'
    );
}
add_action('admin_menu', 'ln_reader_add_import_log_admin_page');

// Import log admin page
function ln_reader_import_log_page() {
    // Handle clear log action
    if (isset($_POST['clear_log']) && wp_verify_nonce($_POST['_wpnonce'], 'clear_import_log')) {
        delete_option('ln_reader_import_log');
        delete_option('ln_reader_import_report');

        // Clear all user meta for dismissed notices
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'ln_reader_import_notice_dismissed_%'");

        echo '<div class="notice notice-success"><p>Import log cleared successfully!</p></div>';
        echo '<div class="wrap"><h1>LN Reader Import Log</h1><p>Import log has been cleared.</p></div>';
        return;
    }

    $import_report = get_option('ln_reader_import_report');

    if (!$import_report) {
        echo '<div class="wrap">';
        echo '<h1>LN Reader Import Log</h1>';
        echo '<p>No import log available.</p>';

        // Check if there's still an import_log option
        $import_log = get_option('ln_reader_import_log');
        if ($import_log) {
            echo '<div class="notice notice-info">';
            echo '<p>There is an active import log. You can clear it using the button below.</p>';
            echo '</div>';

            echo '<form method="post" style="margin-top: 20px;">';
            wp_nonce_field('clear_import_log');
            echo '<input type="submit" name="clear_log" class="button button-secondary" value="Clear Import Log" onclick="return confirm(\'Are you sure you want to clear the import log? This will remove all import warnings.\');">';
            echo '</form>';
        }

        echo '</div>';
        return;
    }

    echo '<div class="wrap">';
    echo '<h1>LN Reader Import Log</h1>';

    // Add clear log button
    echo '<div style="margin: 20px 0; padding: 15px; background: #f8f8f8; border-left: 4px solid #ddd;">';
    echo '<form method="post">';
    wp_nonce_field('clear_import_log');
    echo '<p><strong>Clear Import Log</strong></p>';
    echo '<p>Clearing the import log will remove all warnings and errors from the admin dashboard.</p>';
    echo '<input type="submit" name="clear_log" class="button button-secondary" value="Clear Import Log" onclick="return confirm(\'Are you sure you want to clear the import log? This will remove all import warnings.\');">';
    echo '</form>';
    echo '</div>';

    // Summary
    echo '<h2>Import Summary</h2>';
    echo '<table class="widefat">';
    echo '<tr><td><strong>Start Time:</strong></td><td>' . $import_report['summary']['start_time'] . '</td></tr>';
    echo '<tr><td><strong>End Time:</strong></td><td>' . $import_report['summary']['end_time'] . '</td></tr>';
    echo '<tr><td><strong>Duration:</strong></td><td>' . $import_report['summary']['duration'] . '</td></tr>';
    echo '<tr><td><strong>Status:</strong></td><td>' . $import_report['summary']['status'] . '</td></tr>';
    echo '</table>';

    // Statistics
    echo '<h2>Import Statistics</h2>';
    echo '<table class="widefat">';
    echo '<thead><tr><th>Type</th><th>Processed</th><th>Success</th><th>Failed</th><th>Success Rate</th></tr></thead>';
    echo '<tbody>';
    echo '<tr><td>Posts</td><td>' . $import_report['statistics']['posts']['processed'] . '</td><td>' . $import_report['statistics']['posts']['success'] . '</td><td>' . $import_report['statistics']['posts']['failed'] . '</td><td>' . $import_report['statistics']['posts']['success_rate'] . '%</td></tr>';
    echo '<tr><td>Terms</td><td>' . $import_report['statistics']['terms']['processed'] . '</td><td>' . $import_report['statistics']['terms']['success'] . '</td><td>' . $import_report['statistics']['terms']['failed'] . '</td><td>' . $import_report['statistics']['terms']['success_rate'] . '%</td></tr>';
    echo '</tbody>';
    echo '</table>';

    // Errors
    if (!empty($import_report['errors'])) {
        echo '<h2>Errors (' . count($import_report['errors']) . ')</h2>';
        echo '<table class="widefat">';
        echo '<thead><tr><th>Type</th><th>Message</th><th>Details</th><th>Time</th></tr></thead>';
        echo '<tbody>';
        foreach ($import_report['errors'] as $error) {
            echo '<tr>';
            echo '<td>' . $error['type'] . '</td>';
            echo '<td>' . esc_html($error['error_message']) . '</td>';
            echo '<td>' . esc_html(json_encode($error['post_data'] ?? $error['term_data'] ?? $error['user_data'] ?? array())) . '</td>';
            echo '<td>' . $error['timestamp'] . '</td>';
            echo '</tr>';
        }
        echo '</tbody>';
        echo '</table>';
    }

    // Warnings
    if (!empty($import_report['warnings'])) {
        echo '<h2>Warnings (' . count($import_report['warnings']) . ')</h2>';
        echo '<table class="widefat">';
        echo '<thead><tr><th>Message</th><th>Context</th><th>Time</th></tr></thead>';
        echo '<tbody>';
        foreach ($import_report['warnings'] as $warning) {
            echo '<tr>';
            echo '<td>' . esc_html($warning['message']) . '</td>';
            echo '<td>' . esc_html(json_encode($warning['context'])) . '</td>';
            echo '<td>' . $warning['timestamp'] . '</td>';
            echo '</tr>';
        }
        echo '</tbody>';
        echo '</table>';
    }

    echo '</div>';
}

// Enhanced theme support features for import compatibility
function ln_reader_enhanced_theme_support() {
    // Core WordPress features
    add_theme_support('post-thumbnails');
    add_theme_support('custom-header');
    add_theme_support('custom-background');
    add_theme_support('automatic-feed-links');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');

    // HTML5 support
    add_theme_support('html5', array(
        'comment-list',
        'comment-form',
        'search-form',
        'gallery',
        'caption',
        'style',
        'script'
    ));

    // Post formats support
    add_theme_support('post-formats', array(
        'aside',
        'gallery',
        'link',
        'image',
        'quote',
        'status',
        'video',
        'audio',
        'chat'
    ));

    // Custom post type support
    add_post_type_support('novel', array(
        'title',
        'editor',
        'thumbnail',
        'excerpt',
        'custom-fields',
        'comments',
        'revisions',
        'author',
        'page-attributes'
    ));

    // Ensure attachment support
    add_post_type_support('attachment', array(
        'title',
        'editor',
        'thumbnail',
        'custom-fields'
    ));

    // Widget support
    add_theme_support('widgets');
    add_theme_support('customize-selective-refresh-widgets');

    // Navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'lnreader'),
        'footer' => __('Footer Menu', 'lnreader'),
        'mobile' => __('Mobile Menu', 'lnreader')
    ));

    // Content width for media
    if (!isset($content_width)) {
        $content_width = 1200;
    }

    // Image sizes for import compatibility
    add_image_size('novel-thumbnail', 300, 400, true);
    add_image_size('novel-large', 600, 800, true);
    add_image_size('chapter-image', 800, 600, false);

    // Editor styles
    add_theme_support('editor-styles');
    add_editor_style('editor-style.css');

    // Block editor support
    add_theme_support('wp-block-styles');
    add_theme_support('align-wide');
    add_theme_support('responsive-embeds');

    // Custom fields support for import
    add_theme_support('custom-fields');
}
add_action('after_setup_theme', 'ln_reader_enhanced_theme_support');

// Register additional image sizes for imported content
function ln_reader_register_import_image_sizes() {
    // Sizes commonly used in novel/manga themes
    add_image_size('cover-small', 150, 200, true);
    add_image_size('cover-medium', 300, 400, true);
    add_image_size('cover-large', 450, 600, true);
    add_image_size('banner', 1200, 300, true);
    add_image_size('author-avatar', 100, 100, true);
}
add_action('init', 'ln_reader_register_import_image_sizes');

// Enhance customizer support for import compatibility
function ln_reader_customizer_import_support($wp_customize) {
    // Add sections for imported content
    $wp_customize->add_section('ln_reader_import_settings', array(
        'title' => __('Import Settings', 'lnreader'),
        'priority' => 30,
        'description' => __('Settings related to content import and compatibility', 'lnreader')
    ));

    // Import compatibility mode
    $wp_customize->add_setting('ln_reader_import_compatibility_mode', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean'
    ));

    $wp_customize->add_control('ln_reader_import_compatibility_mode', array(
        'label' => __('Enable Import Compatibility Mode', 'lnreader'),
        'description' => __('Enables enhanced compatibility features for importing content from other themes', 'lnreader'),
        'section' => 'ln_reader_import_settings',
        'type' => 'checkbox'
    ));

    // Auto-convert series to novels
    $wp_customize->add_setting('ln_reader_auto_convert_series', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean'
    ));

    $wp_customize->add_control('ln_reader_auto_convert_series', array(
        'label' => __('Auto-convert Series to Novels', 'lnreader'),
        'description' => __('Automatically convert imported "series" post type to "novel" post type', 'lnreader'),
        'section' => 'ln_reader_import_settings',
        'type' => 'checkbox'
    ));

    // Preserve original URLs
    $wp_customize->add_setting('ln_reader_preserve_urls', array(
        'default' => false,
        'sanitize_callback' => 'wp_validate_boolean'
    ));

    $wp_customize->add_control('ln_reader_preserve_urls', array(
        'label' => __('Preserve Original URLs', 'lnreader'),
        'description' => __('Attempt to preserve original post URLs during import', 'lnreader'),
        'section' => 'ln_reader_import_settings',
        'type' => 'checkbox'
    ));
}
add_action('customize_register', 'ln_reader_customizer_import_support');

// Widget areas for import compatibility and AdSense placements
function ln_reader_import_widget_areas() {
    // Ensure all common widget areas exist including AdSense-friendly placements
    $widget_areas = array(
        'sidebar-main' => array(
            'name' => __('Main Sidebar', 'lnreader'),
            'description' => __('Main sidebar widget area', 'lnreader')
        ),
        'sidebar-novel' => array(
            'name' => __('Novel Sidebar', 'lnreader'),
            'description' => __('Sidebar for novel pages', 'lnreader')
        ),
        'footer-1' => array(
            'name' => __('Footer 1', 'lnreader'),
            'description' => __('First footer widget area', 'lnreader')
        ),
        'footer-2' => array(
            'name' => __('Footer 2', 'lnreader'),
            'description' => __('Second footer widget area', 'lnreader')
        ),
        'footer-3' => array(
            'name' => __('Footer 3', 'lnreader'),
            'description' => __('Third footer widget area', 'lnreader')
        ),
        'header-ads' => array(
            'name' => __('Header Ads', 'lnreader'),
            'description' => __('Header advertisement area below navigation', 'lnreader')
        ),
        'content-top-ads' => array(
            'name' => __('Content Top Ads', 'lnreader'),
            'description' => __('Advertisement area at the top of main content', 'lnreader')
        ),
        'content-middle-ads' => array(
            'name' => __('Content Middle Ads', 'lnreader'),
            'description' => __('Advertisement area in the middle of content', 'lnreader')
        ),
        'content-bottom-ads' => array(
            'name' => __('Content Bottom Ads', 'lnreader'),
            'description' => __('Advertisement area at the bottom of content', 'lnreader')
        ),
        'sidebar-top-ads' => array(
            'name' => __('Sidebar Top Ads', 'lnreader'),
            'description' => __('Advertisement area at the top of sidebar', 'lnreader')
        ),
        'sidebar-middle-ads' => array(
            'name' => __('Sidebar Middle Ads', 'lnreader'),
            'description' => __('Advertisement area in the middle of sidebar', 'lnreader')
        ),
        'sidebar-bottom-ads' => array(
            'name' => __('Sidebar Bottom Ads', 'lnreader'),
            'description' => __('Advertisement area at the bottom of sidebar', 'lnreader')
        ),
        'footer-ads' => array(
            'name' => __('Footer Ads', 'lnreader'),
            'description' => __('Advertisement area above footer content', 'lnreader')
        ),
        'chapter-top-ads' => array(
            'name' => __('Chapter Top Ads', 'lnreader'),
            'description' => __('Advertisement area at the top of chapter content', 'lnreader')
        ),
        'chapter-bottom-ads' => array(
            'name' => __('Chapter Bottom Ads', 'lnreader'),
            'description' => __('Advertisement area at the bottom of chapter content', 'lnreader')
        ),
        'novel-page-ads' => array(
            'name' => __('Novel Page Ads', 'lnreader'),
            'description' => __('Advertisement area on novel detail pages', 'lnreader')
        )
    );

    foreach ($widget_areas as $id => $area) {
        register_sidebar(array(
            'id' => $id,
            'name' => $area['name'],
            'description' => $area['description'],
            'before_widget' => '<div id="%1$s" class="widget ad-widget %2$s">',
            'after_widget' => '</div>',
            'before_title' => '<h3 class="widget-title">',
            'after_title' => '</h3>'
        ));
    }
}
add_action('widgets_init', 'ln_reader_import_widget_areas');

// Google Site Kit & Auto Ads compatibility functions
function ln_reader_is_site_kit_active() {
    return class_exists('Google\\Site_Kit\\Plugin') || class_exists('Google\\Site_Kit\\Core\\Modules\\AdSense\\AdSense');
}

function ln_reader_is_auto_ads_active() {
    // Check for Site Kit plugin and Auto Ads
    if (!ln_reader_is_site_kit_active()) {
        return false;
    }

    // Check for Auto Ads specific indicators in Site Kit
    if (function_exists('googlesitekit_get_option')) {
        $adsense_settings = googlesitekit_get_option('googlesitekit_adsense_settings');
        if (isset($adsense_settings['autoAdsEnabled']) && $adsense_settings['autoAdsEnabled']) {
            return true;
        }
    }

    // Alternative check for Auto Ads script presence
    global $wp_scripts;
    if (isset($wp_scripts->registered['google_gtagjs']) ||
        isset($wp_scripts->registered['googlesitekit-adsense'])) {
        return true;
    }

    return false;
}

// Enhanced AdSense-friendly content insertion with Auto Ads compatibility
function ln_reader_smart_content_ads($content) {
    // Only insert ads on single posts/pages and if user is not an admin
    if (!is_single() && !is_page() || current_user_can('administrator')) {
        return $content;
    }

    // Don't insert ads in admin area or feeds
    if (is_admin() || is_feed()) {
        return $content;
    }

    // Check if Auto Ads is active - if so, let Auto Ads handle content insertion
    if (ln_reader_is_auto_ads_active()) {
        // Add a filter to allow manual override
        if (!apply_filters('ln_reader_force_manual_content_ads', false)) {
            return $content;
        }
    }

    // Proceed with manual content ad insertion
    return ln_reader_insert_ads_in_content($content);
}

// Original content ad insertion function (now called by smart function)
function ln_reader_insert_ads_in_content($content) {
    // Split content into paragraphs
    $paragraphs = explode('</p>', $content);
    $paragraph_count = count($paragraphs);

    // Only proceed if we have enough paragraphs
    if ($paragraph_count < 3) {
        return $content;
    }

    $new_content = '';
    $ad_inserted = false;

    foreach ($paragraphs as $index => $paragraph) {
        $new_content .= $paragraph;

        // Add closing </p> tag if not the last paragraph
        if ($index < $paragraph_count - 1) {
            $new_content .= '</p>';
        }

        // Insert ad after 3rd paragraph (middle of content)
        if ($index == 2 && !$ad_inserted && is_active_sidebar('content-middle-ads')) {
            $new_content .= '<div class="content-middle-ads auto-ads-compatible">';
            ob_start();
            dynamic_sidebar('content-middle-ads');
            $new_content .= ob_get_clean();
            $new_content .= '</div>';
            $ad_inserted = true;
        }
    }

    return $new_content;
}

// Apply smart content ad insertion filter
add_filter('the_content', 'ln_reader_smart_content_ads');

// Function to display ad widget if active
function ln_reader_display_ad_widget($widget_id, $wrapper_class = '') {
    if (is_active_sidebar($widget_id)) {
        $class = $wrapper_class ? $wrapper_class : $widget_id;
        echo '<div class="' . esc_attr($class) . '">';
        dynamic_sidebar($widget_id);
        echo '</div>';
    }
}

// Function to check if any ad widgets are active
function ln_reader_has_ads() {
    $ad_widgets = array(
        'header-ads',
        'content-top-ads',
        'content-middle-ads',
        'content-bottom-ads',
        'sidebar-top-ads',
        'sidebar-middle-ads',
        'sidebar-bottom-ads',
        'footer-ads',
        'chapter-top-ads',
        'chapter-bottom-ads',
        'novel-page-ads'
    );

    foreach ($ad_widgets as $widget) {
        if (is_active_sidebar($widget)) {
            return true;
        }
    }
    return false;
}

// Add AdSense-friendly meta tags
function ln_reader_adsense_meta_tags() {
    if (ln_reader_has_ads()) {
        echo '<meta name="google-adsense-platform-account" content="ca-host-pub-****************">' . "\n";
        echo '<meta name="google-adsense-platform-domain" content="sitekit.withgoogle.com">' . "\n";
    }
}
add_action('wp_head', 'ln_reader_adsense_meta_tags');

// Add structured data for better AdSense targeting
function ln_reader_adsense_structured_data() {
    if (is_single() && get_post_type() == 'post') {
        $novel_id = get_post_meta(get_the_ID(), '_novel_id', true);
        if ($novel_id) {
            $novel = get_post($novel_id);
            if ($novel) {
                $schema = array(
                    '@context' => 'https://schema.org',
                    '@type' => 'Article',
                    'headline' => get_the_title(),
                    'author' => array(
                        '@type' => 'Person',
                        'name' => get_the_author()
                    ),
                    'datePublished' => get_the_date('c'),
                    'dateModified' => get_the_modified_date('c'),
                    'publisher' => array(
                        '@type' => 'Organization',
                        'name' => get_bloginfo('name')
                    ),
                    'mainEntityOfPage' => array(
                        '@type' => 'WebPage',
                        '@id' => get_permalink()
                    ),
                    'genre' => 'Light Novel',
                    'about' => $novel->post_title
                );

                echo '<script type="application/ld+json">' . json_encode($schema) . '</script>' . "\n";
            }
        }
    }
}
add_action('wp_head', 'ln_reader_adsense_structured_data');

// Add Auto Ads compatibility JavaScript
function ln_reader_auto_ads_compatibility_script() {
    if (ln_reader_is_site_kit_active() || ln_reader_has_ads()) {
        ?>
        <script>
        // Auto Ads compatibility enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure Auto Ads work with dynamic content
            if (typeof adsbygoogle !== 'undefined') {
                // Monitor for Auto Ads insertion
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && node.classList && node.classList.contains('google-auto-placed')) {
                                // Auto Ad was inserted, apply theme compatibility
                                applyAutoAdThemeCompatibility(node);
                            }
                        });
                    });
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }

            // Apply theme compatibility to Auto Ads
            function applyAutoAdThemeCompatibility(adElement) {
                const currentTheme = document.querySelector('.theme-content-wrapper')?.getAttribute('data-theme') || 'light';

                if (currentTheme === 'dark') {
                    adElement.style.background = 'rgba(26, 32, 44, 0.5)';
                    adElement.style.borderColor = '#4a5568';
                } else if (currentTheme === 'sepia') {
                    adElement.style.background = 'rgba(240, 230, 210, 0.5)';
                    adElement.style.borderColor = '#e8dcbf';
                }

                // Ensure proper spacing in reading container
                if (adElement.closest('.reading-container') || adElement.closest('.chapter-content')) {
                    adElement.style.margin = '2rem 0';
                    adElement.style.padding = '1rem 0';
                    adElement.style.borderTop = '1px solid #e9ecef';
                    adElement.style.borderBottom = '1px solid #e9ecef';
                }
            }

            // Handle theme changes for existing Auto Ads
            document.addEventListener('themeChanged', function(e) {
                const autoAds = document.querySelectorAll('.google-auto-placed');
                autoAds.forEach(applyAutoAdThemeCompatibility);
            });

            // Refresh Auto Ads after AJAX content loads (if applicable)
            document.addEventListener('contentLoaded', function() {
                if (typeof adsbygoogle !== 'undefined' && window.adsbygoogle) {
                    try {
                        (adsbygoogle = window.adsbygoogle || []).push({});
                    } catch (e) {
                        console.log('Auto Ads refresh skipped');
                    }
                }
            });
        });
        </script>
        <?php
    }
}
add_action('wp_footer', 'ln_reader_auto_ads_compatibility_script');

// Add Site Kit compatibility meta tags
function ln_reader_site_kit_compatibility_meta() {
    if (ln_reader_is_site_kit_active()) {
        echo '<meta name="google-site-verification" content="theme-compatible">' . "\n";
        echo '<meta name="adsense-theme-compatibility" content="ln-reader-v1.2">' . "\n";
    }
}
add_action('wp_head', 'ln_reader_site_kit_compatibility_meta', 1);

// AdSense configuration helper functions
function ln_reader_get_adsense_config_recommendations() {
    $recommendations = array();

    if (ln_reader_is_site_kit_active()) {
        $recommendations['site_kit'] = array(
            'status' => 'active',
            'message' => 'Google Site Kit detected. Auto Ads compatibility enabled.',
            'action' => 'Configure Auto Ads in Site Kit dashboard for optimal performance.'
        );

        if (ln_reader_is_auto_ads_active()) {
            $recommendations['auto_ads'] = array(
                'status' => 'active',
                'message' => 'Auto Ads detected. Manual content ads are automatically disabled.',
                'action' => 'Consider using manual widgets only for header, sidebar, and footer placements.'
            );
        } else {
            $recommendations['auto_ads'] = array(
                'status' => 'inactive',
                'message' => 'Auto Ads not detected. Manual ad placements are active.',
                'action' => 'Enable Auto Ads in AdSense dashboard for optimized content placement.'
            );
        }
    } else {
        $recommendations['site_kit'] = array(
            'status' => 'inactive',
            'message' => 'Google Site Kit not detected.',
            'action' => 'Install Google Site Kit plugin for enhanced AdSense integration.'
        );
    }

    // Check active widget areas
    $active_widgets = array();
    $ad_widgets = array(
        'header-ads', 'content-top-ads', 'content-middle-ads', 'content-bottom-ads',
        'sidebar-top-ads', 'sidebar-middle-ads', 'sidebar-bottom-ads',
        'footer-ads', 'chapter-top-ads', 'chapter-bottom-ads', 'novel-page-ads'
    );

    foreach ($ad_widgets as $widget) {
        if (is_active_sidebar($widget)) {
            $active_widgets[] = $widget;
        }
    }

    $recommendations['widgets'] = array(
        'active_count' => count($active_widgets),
        'active_widgets' => $active_widgets,
        'message' => count($active_widgets) . ' ad widget areas are currently active.',
        'action' => count($active_widgets) > 5 ? 'Consider reducing manual widgets if using Auto Ads.' : 'Good balance of manual ad placements.'
    );

    return $recommendations;
}

// Admin notice for AdSense configuration
function ln_reader_adsense_admin_notice() {
    if (!current_user_can('manage_options')) {
        return;
    }

    $screen = get_current_screen();
    if ($screen->id !== 'appearance_page_theme-options' && $screen->id !== 'widgets') {
        return;
    }

    $recommendations = ln_reader_get_adsense_config_recommendations();

    if (isset($recommendations['auto_ads']) && $recommendations['auto_ads']['status'] === 'active') {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>LN Reader AdSense:</strong> ' . $recommendations['auto_ads']['message'] . '</p>';
        echo '<p><em>Tip:</em> ' . $recommendations['auto_ads']['action'] . '</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'ln_reader_adsense_admin_notice');

// Add AdSense status to admin bar for administrators
function ln_reader_admin_bar_adsense_status($wp_admin_bar) {
    if (!current_user_can('manage_options')) {
        return;
    }

    $recommendations = ln_reader_get_adsense_config_recommendations();
    $auto_ads_status = isset($recommendations['auto_ads']) ? $recommendations['auto_ads']['status'] : 'unknown';
    $widget_count = $recommendations['widgets']['active_count'];

    $wp_admin_bar->add_node(array(
        'id' => 'ln-reader-adsense',
        'title' => 'AdSense: ' . ucfirst($auto_ads_status) . ' (' . $widget_count . ' widgets)',
        'href' => admin_url('widgets.php'),
        'meta' => array(
            'title' => 'Click to manage AdSense widget areas'
        )
    ));
}
add_action('admin_bar_menu', 'ln_reader_admin_bar_adsense_status', 100);

// Performance Optimizations
function ln_reader_performance_optimizations() {
    // Add preload hints for critical resources
    add_action('wp_head', function() {
        // Preload critical fonts
        echo '<link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
        echo '<link rel="preload" href="https://fonts.googleapis.com/css2?family=Merriweather:wght@300;400;700&display=swap" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';

        // Preload critical CSS
        echo '<link rel="preload" href="' . get_template_directory_uri() . '/css/adsense-styles.css" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';

        // DNS prefetch for external resources
        echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">';
        echo '<link rel="dns-prefetch" href="//fonts.gstatic.com">';
        echo '<link rel="dns-prefetch" href="//cdn.jsdelivr.net">';
        echo '<link rel="dns-prefetch" href="//pagead2.googlesyndication.com">';
        echo '<link rel="dns-prefetch" href="//googleads.g.doubleclick.net">';
    }, 1);

    // Optimize images with lazy loading
    add_filter('wp_get_attachment_image_attributes', function($attr, $attachment, $size) {
        if (!is_admin() && !wp_is_mobile()) {
            $attr['loading'] = 'lazy';
            $attr['decoding'] = 'async';
        }
        return $attr;
    }, 10, 3);

    // Add critical CSS inline for above-the-fold content
    add_action('wp_head', function() {
        if (is_front_page() || is_home()) {
            echo '<style id="critical-css">';
            echo ':root{--primary-color:#1a73e8;--secondary-color:#34a853;--accent-color:#ea4335;--text-primary:#202124;--text-secondary:#5f6368;--background-primary:#ffffff;--background-secondary:#f8f9fa;--border-color:#dadce0;--shadow-light:0 1px 3px rgba(60,64,67,0.1);--font-family-primary:"Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;--font-size-base:1rem;--line-height-normal:1.5;--spacing-md:1rem;--spacing-lg:1.5rem;--radius-md:0.375rem;--transition-fast:0.15s ease-in-out}';
            echo 'body{font-family:var(--font-family-primary);font-size:var(--font-size-base);line-height:var(--line-height-normal);background:var(--background-secondary);color:var(--text-primary);margin:0;padding:0}';
            echo '.site-header{position:sticky;top:0;z-index:1000;background:var(--background-primary);box-shadow:var(--shadow-light)}';
            echo '.navbar{background-color:var(--background-primary);border-bottom:1px solid var(--border-color);padding:var(--spacing-md) 0}';
            echo '.container{max-width:1200px;margin:0 auto;padding:0 var(--spacing-md)}';
            echo '</style>';
        }
    }, 2);

    // Defer non-critical JavaScript
    add_filter('script_loader_tag', function($tag, $handle, $src) {
        $defer_scripts = ['bootstrap', 'jquery-ui', 'wp-embed'];
        if (in_array($handle, $defer_scripts)) {
            return str_replace(' src', ' defer src', $tag);
        }
        return $tag;
    }, 10, 3);

    // Optimize database queries
    add_action('pre_get_posts', function($query) {
        if (!is_admin() && $query->is_main_query()) {
            // Limit posts per page for better performance
            if (is_home() || is_archive()) {
                $query->set('posts_per_page', 12);
            }

            // Optimize novel queries
            if (is_post_type_archive('novel')) {
                $query->set('meta_key', '_novel_status');
                $query->set('meta_value', ['ongoing', 'completed']);
                $query->set('meta_compare', 'IN');
                $query->set('orderby', 'modified');
                $query->set('order', 'DESC');
            }
        }
    });
}
add_action('init', 'ln_reader_performance_optimizations');

// Add WebP support for better image compression
function ln_reader_add_webp_support() {
    add_filter('wp_check_filetype_and_ext', function($data, $file, $filename, $mimes) {
        $filetype = wp_check_filetype($filename, $mimes);
        return [
            'ext'             => $filetype['ext'],
            'type'            => $filetype['type'],
            'proper_filename' => $data['proper_filename']
        ];
    }, 10, 4);

    add_filter('upload_mimes', function($mimes) {
        $mimes['webp'] = 'image/webp';
        return $mimes;
    });
}
add_action('init', 'ln_reader_add_webp_support');

// Implement service worker for caching (basic implementation)
function ln_reader_add_service_worker() {
    if (!is_admin()) {
        add_action('wp_footer', function() {
            echo '<script>';
            echo 'if("serviceWorker" in navigator){';
            echo 'window.addEventListener("load",function(){';
            echo 'navigator.serviceWorker.register("' . get_template_directory_uri() . '/sw.js")';
            echo '.then(function(registration){console.log("SW registered: ",registration);})';
            echo '.catch(function(registrationError){console.log("SW registration failed: ",registrationError);});';
            echo '});';
            echo '}';
            echo '</script>';
        });
    }
}
add_action('init', 'ln_reader_add_service_worker');

// Enqueue enhanced theme scripts
function ln_reader_enqueue_enhanced_scripts() {
    wp_enqueue_script(
        'ln-reader-enhancements',
        get_template_directory_uri() . '/js/theme-enhancements.js',
        array('jquery'),
        LN_READER_VERSION,
        true
    );

    // Localize script with necessary data
    wp_localize_script('ln-reader-enhancements', 'lnReaderData', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('ln_reader_nonce'),
        'strings' => array(
            'bookmark_added' => __('Added to bookmarks!', 'ln-reader'),
            'bookmark_removed' => __('Removed from bookmarks!', 'ln-reader'),
            'rating_saved' => __('Rating saved!', 'ln-reader'),
            'url_copied' => __('URL copied to clipboard!', 'ln-reader'),
            'error_occurred' => __('An error occurred. Please try again.', 'ln-reader'),
        )
    ));

    // Add reading progress bar on chapter pages
    if (is_single() && get_post_type() == 'post') {
        add_action('wp_footer', function() {
            echo '<div class="reading-progress-container"><div class="reading-progress"></div></div>';
        });
    }
}
add_action('wp_enqueue_scripts', 'ln_reader_enqueue_enhanced_scripts');

// Breadcrumb Navigation System
function ln_reader_breadcrumbs() {
    // Don't show on homepage
    if (is_home() || is_front_page()) {
        return;
    }

    $breadcrumbs = array();
    $home_title = get_bloginfo('name');

    // Add home link
    $breadcrumbs[] = array(
        'title' => $home_title,
        'url' => home_url('/'),
        'current' => false
    );

    if (is_single()) {
        if (get_post_type() == 'novel') {
            // Novel single page
            $breadcrumbs[] = array(
                'title' => __('Novels', 'ln-reader'),
                'url' => get_post_type_archive_link('novel'),
                'current' => false
            );

            // Add genres if available
            $genres = get_the_terms(get_the_ID(), 'novel_genre');
            if ($genres && !is_wp_error($genres)) {
                $primary_genre = $genres[0];
                $breadcrumbs[] = array(
                    'title' => $primary_genre->name,
                    'url' => get_term_link($primary_genre),
                    'current' => false
                );
            }

            $breadcrumbs[] = array(
                'title' => get_the_title(),
                'url' => '',
                'current' => true
            );
        } elseif (get_post_type() == 'post') {
            // Chapter page
            $novel_id = get_post_meta(get_the_ID(), '_novel_id', true);
            if ($novel_id) {
                $novel = get_post($novel_id);
                if ($novel) {
                    $breadcrumbs[] = array(
                        'title' => __('Novels', 'ln-reader'),
                        'url' => get_post_type_archive_link('novel'),
                        'current' => false
                    );

                    $breadcrumbs[] = array(
                        'title' => $novel->post_title,
                        'url' => get_permalink($novel->ID),
                        'current' => false
                    );

                    $chapter_number = get_post_meta(get_the_ID(), '_chapter_number', true);
                    $breadcrumbs[] = array(
                        'title' => sprintf(__('Chapter %s', 'ln-reader'), $chapter_number),
                        'url' => '',
                        'current' => true
                    );
                }
            }
        }
    } elseif (is_page()) {
        // Regular page
        $breadcrumbs[] = array(
            'title' => get_the_title(),
            'url' => '',
            'current' => true
        );
    } elseif (is_post_type_archive('novel')) {
        // Novel archive
        $breadcrumbs[] = array(
            'title' => __('Novels', 'ln-reader'),
            'url' => '',
            'current' => true
        );
    } elseif (is_tax('novel_genre')) {
        // Genre archive
        $term = get_queried_object();
        $breadcrumbs[] = array(
            'title' => __('Novels', 'ln-reader'),
            'url' => get_post_type_archive_link('novel'),
            'current' => false
        );

        $breadcrumbs[] = array(
            'title' => $term->name,
            'url' => '',
            'current' => true
        );
    } elseif (is_search()) {
        // Search results
        $breadcrumbs[] = array(
            'title' => sprintf(__('Search Results for "%s"', 'ln-reader'), get_search_query()),
            'url' => '',
            'current' => true
        );
    } elseif (is_404()) {
        // 404 page
        $breadcrumbs[] = array(
            'title' => __('Page Not Found', 'ln-reader'),
            'url' => '',
            'current' => true
        );
    }

    // Output breadcrumbs
    if (!empty($breadcrumbs)) {
        echo '<nav class="breadcrumb-navigation" aria-label="' . esc_attr__('Breadcrumb', 'ln-reader') . '">';
        echo '<ol class="breadcrumb" itemscope itemtype="https://schema.org/BreadcrumbList">';

        foreach ($breadcrumbs as $index => $crumb) {
            $position = $index + 1;
            echo '<li class="breadcrumb-item' . ($crumb['current'] ? ' active' : '') . '" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';

            if (!$crumb['current'] && !empty($crumb['url'])) {
                echo '<a href="' . esc_url($crumb['url']) . '" itemprop="item">';
                echo '<span itemprop="name">' . esc_html($crumb['title']) . '</span>';
                echo '</a>';
            } else {
                echo '<span itemprop="name">' . esc_html($crumb['title']) . '</span>';
            }

            echo '<meta itemprop="position" content="' . esc_attr($position) . '">';
            echo '</li>';
        }

        echo '</ol>';
        echo '</nav>';
    }
}

// Enhanced menu support for import
function ln_reader_enhanced_menu_support() {
    // Ensure menu locations are available
    $menu_locations = get_registered_nav_menus();

    if (empty($menu_locations)) {
        register_nav_menus(array(
            'primary' => __('Primary Menu', 'lnreader'),
            'secondary' => __('Secondary Menu', 'lnreader'),
            'footer' => __('Footer Menu', 'lnreader'),
            'mobile' => __('Mobile Menu', 'lnreader'),
            'social' => __('Social Menu', 'lnreader')
        ));
    }

    // Add menu support for custom post types
    add_post_type_support('novel', 'nav-menus');
}
add_action('init', 'ln_reader_enhanced_menu_support');

// Comment system compatibility
function ln_reader_comment_system_compatibility() {
    // Ensure comments work on all post types
    add_post_type_support('novel', 'comments');
    add_post_type_support('page', 'comments');

    // Comment form enhancements
    add_filter('comment_form_defaults', 'ln_reader_enhance_comment_form');
}
add_action('init', 'ln_reader_comment_system_compatibility');

// Enhance comment form for import compatibility
function ln_reader_enhance_comment_form($defaults) {
    $defaults['title_reply'] = __('Leave a Comment', 'lnreader');
    $defaults['title_reply_to'] = __('Leave a Reply to %s', 'lnreader');
    $defaults['cancel_reply_link'] = __('Cancel Reply', 'lnreader');
    $defaults['label_submit'] = __('Post Comment', 'lnreader');

    return $defaults;
}

// Feed support for import compatibility
function ln_reader_feed_compatibility() {
    // Add feeds for custom post types
    add_action('init', function() {
        add_feed('novels', 'ln_reader_novels_feed');
        add_feed('chapters', 'ln_reader_chapters_feed');
    });

    // Include custom post types in main feed
    add_filter('request', 'ln_reader_include_custom_post_types_in_feed');
}
add_action('init', 'ln_reader_feed_compatibility');

// Include custom post types in feeds
function ln_reader_include_custom_post_types_in_feed($query_vars) {
    if (isset($query_vars['feed']) && !isset($query_vars['post_type'])) {
        $query_vars['post_type'] = array('post', 'novel');
    }
    return $query_vars;
}

// Novels feed template
function ln_reader_novels_feed() {
    $posts = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => 10,
        'post_status' => 'publish'
    ));

    header('Content-Type: application/rss+xml; charset=' . get_option('blog_charset'), true);
    echo '<?xml version="1.0" encoding="' . get_option('blog_charset') . '"?>';
    ?>
    <rss version="2.0">
        <channel>
            <title><?php bloginfo_rss('name'); ?> - Novels</title>
            <link><?php bloginfo_rss('url'); ?></link>
            <description><?php bloginfo_rss('description'); ?></description>
            <?php foreach ($posts as $post): setup_postdata($post); ?>
            <item>
                <title><?php the_title_rss(); ?></title>
                <link><?php the_permalink_rss(); ?></link>
                <description><?php the_excerpt_rss(); ?></description>
                <pubDate><?php echo mysql2date('D, d M Y H:i:s +0000', get_post_time('Y-m-d H:i:s', true), false); ?></pubDate>
                <guid><?php the_guid(); ?></guid>
            </item>
            <?php endforeach; wp_reset_postdata(); ?>
        </channel>
    </rss>
    <?php
}

// Chapters feed template
function ln_reader_chapters_feed() {
    $posts = get_posts(array(
        'post_type' => 'post',
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'compare' => 'EXISTS'
            )
        ),
        'posts_per_page' => 20,
        'post_status' => 'publish'
    ));

    header('Content-Type: application/rss+xml; charset=' . get_option('blog_charset'), true);
    echo '<?xml version="1.0" encoding="' . get_option('blog_charset') . '"?>';
    ?>
    <rss version="2.0">
        <channel>
            <title><?php bloginfo_rss('name'); ?> - Latest Chapters</title>
            <link><?php bloginfo_rss('url'); ?></link>
            <description><?php bloginfo_rss('description'); ?></description>
            <?php foreach ($posts as $post): setup_postdata($post); ?>
            <item>
                <title><?php the_title_rss(); ?></title>
                <link><?php the_permalink_rss(); ?></link>
                <description><?php the_excerpt_rss(); ?></description>
                <pubDate><?php echo mysql2date('D, d M Y H:i:s +0000', get_post_time('Y-m-d H:i:s', true), false); ?></pubDate>
                <guid><?php the_guid(); ?></guid>
            </item>
            <?php endforeach; wp_reset_postdata(); ?>
        </channel>
    </rss>
    <?php
}

// Import validation and testing system
function ln_reader_import_validation_system() {
    // Pre-import validation
    add_action('import_start', 'ln_reader_pre_import_validation');
    add_action('import_end', 'ln_reader_post_import_validation');

    // Real-time validation during import
    add_filter('wp_import_post_data_processed', 'ln_reader_validate_post_data', 5, 2);
    add_action('wp_import_insert_post', 'ln_reader_validate_imported_post', 35, 4);
}
add_action('init', 'ln_reader_import_validation_system');

// Pre-import validation
function ln_reader_pre_import_validation() {
    $validation_results = array(
        'timestamp' => current_time('mysql'),
        'pre_import_checks' => array(),
        'warnings' => array(),
        'errors' => array()
    );

    // Check theme compatibility
    $theme_check = ln_reader_validate_theme_compatibility();
    $validation_results['pre_import_checks']['theme_compatibility'] = $theme_check;

    // Check required plugins
    $plugin_check = ln_reader_validate_required_plugins();
    $validation_results['pre_import_checks']['required_plugins'] = $plugin_check;

    // Check server requirements
    $server_check = ln_reader_validate_server_requirements();
    $validation_results['pre_import_checks']['server_requirements'] = $server_check;

    // Check database capacity
    $db_check = ln_reader_validate_database_capacity();
    $validation_results['pre_import_checks']['database_capacity'] = $db_check;

    // Check file permissions
    $permissions_check = ln_reader_validate_file_permissions();
    $validation_results['pre_import_checks']['file_permissions'] = $permissions_check;

    update_option('ln_reader_import_validation', $validation_results);

    error_log('LN Reader Import: Pre-import validation completed');
}

// Validate theme compatibility
function ln_reader_validate_theme_compatibility() {
    $checks = array(
        'novel_post_type_registered' => post_type_exists('novel'),
        'novel_genre_taxonomy_registered' => taxonomy_exists('novel_genre'),
        'novel_tag_taxonomy_registered' => taxonomy_exists('novel_tag'),
        'post_thumbnails_supported' => current_theme_supports('post-thumbnails'),
        'custom_fields_supported' => current_theme_supports('custom-fields'),
        'menus_supported' => current_theme_supports('menus'),
        'widgets_supported' => current_theme_supports('widgets')
    );

    $passed = array_filter($checks);
    $failed = array_diff_key($checks, $passed);

    return array(
        'status' => empty($failed) ? 'pass' : 'warning',
        'passed' => count($passed),
        'total' => count($checks),
        'failed_checks' => array_keys($failed),
        'details' => $checks
    );
}

// Validate required plugins
function ln_reader_validate_required_plugins() {
    $required_plugins = array(
        'wordpress-importer/wordpress-importer.php' => 'WordPress Importer'
    );

    $active_plugins = get_option('active_plugins', array());
    $missing_plugins = array();

    foreach ($required_plugins as $plugin_file => $plugin_name) {
        if (!in_array($plugin_file, $active_plugins)) {
            $missing_plugins[] = $plugin_name;
        }
    }

    return array(
        'status' => empty($missing_plugins) ? 'pass' : 'error',
        'missing_plugins' => $missing_plugins,
        'message' => empty($missing_plugins) ? 'All required plugins are active' : 'Missing required plugins: ' . implode(', ', $missing_plugins)
    );
}

// Validate server requirements
function ln_reader_validate_server_requirements() {
    $requirements = array(
        'php_version' => array(
            'required' => '7.4',
            'current' => PHP_VERSION,
            'status' => version_compare(PHP_VERSION, '7.4', '>=') ? 'pass' : 'error'
        ),
        'memory_limit' => array(
            'required' => '256M',
            'current' => ini_get('memory_limit'),
            'status' => ln_reader_compare_memory_limit(ini_get('memory_limit'), '256M') ? 'pass' : 'warning'
        ),
        'max_execution_time' => array(
            'required' => '300',
            'current' => ini_get('max_execution_time'),
            'status' => (int)ini_get('max_execution_time') >= 300 || ini_get('max_execution_time') == 0 ? 'pass' : 'warning'
        ),
        'upload_max_filesize' => array(
            'required' => '64M',
            'current' => ini_get('upload_max_filesize'),
            'status' => ln_reader_compare_memory_limit(ini_get('upload_max_filesize'), '64M') ? 'pass' : 'warning'
        )
    );

    $errors = array_filter($requirements, function($req) { return $req['status'] === 'error'; });
    $warnings = array_filter($requirements, function($req) { return $req['status'] === 'warning'; });

    return array(
        'status' => !empty($errors) ? 'error' : (!empty($warnings) ? 'warning' : 'pass'),
        'requirements' => $requirements,
        'errors' => count($errors),
        'warnings' => count($warnings)
    );
}

// Compare memory limit values
function ln_reader_compare_memory_limit($current, $required) {
    $current_bytes = ln_reader_convert_to_bytes($current);
    $required_bytes = ln_reader_convert_to_bytes($required);

    return $current_bytes >= $required_bytes;
}

// Convert memory limit to bytes
function ln_reader_convert_to_bytes($value) {
    $value = trim($value);
    $last = strtolower($value[strlen($value)-1]);
    $value = (int)$value;

    switch($last) {
        case 'g': $value *= 1024;
        case 'm': $value *= 1024;
        case 'k': $value *= 1024;
    }

    return $value;
}

// Validate database capacity
function ln_reader_validate_database_capacity() {
    global $wpdb;

    // Check available disk space
    $upload_dir = wp_upload_dir();
    $free_space = disk_free_space($upload_dir['basedir']);
    $required_space = 100 * 1024 * 1024; // 100MB minimum

    // Check database size
    $db_size_query = $wpdb->get_var("
        SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB'
        FROM information_schema.tables
        WHERE table_schema='{$wpdb->dbname}'
    ");

    return array(
        'status' => $free_space >= $required_space ? 'pass' : 'error',
        'free_space_mb' => round($free_space / 1024 / 1024, 1),
        'required_space_mb' => round($required_space / 1024 / 1024, 1),
        'database_size_mb' => $db_size_query,
        'message' => $free_space >= $required_space ? 'Sufficient disk space available' : 'Insufficient disk space for import'
    );
}

// Validate file permissions
function ln_reader_validate_file_permissions() {
    $upload_dir = wp_upload_dir();
    $paths_to_check = array(
        'uploads_dir' => $upload_dir['basedir'],
        'wp_content' => WP_CONTENT_DIR,
        'theme_dir' => get_template_directory()
    );

    $permissions = array();
    foreach ($paths_to_check as $name => $path) {
        $permissions[$name] = array(
            'path' => $path,
            'writable' => is_writable($path),
            'readable' => is_readable($path)
        );
    }

    $failed = array_filter($permissions, function($perm) {
        return !$perm['writable'] || !$perm['readable'];
    });

    return array(
        'status' => empty($failed) ? 'pass' : 'error',
        'permissions' => $permissions,
        'failed_paths' => array_keys($failed),
        'message' => empty($failed) ? 'All required paths are accessible' : 'Some paths have permission issues'
    );
}

// Validate post data during import
function ln_reader_validate_post_data($postdata, $post) {
    $validation_errors = array();

    // Validate required fields
    if (empty($postdata['post_title'])) {
        $validation_errors[] = 'Missing post title';
    }

    if (empty($postdata['post_type'])) {
        $validation_errors[] = 'Missing post type';
    }

    // Validate post type
    if (!empty($postdata['post_type']) && !post_type_exists($postdata['post_type'])) {
        // Check if it's a convertible type
        if ($postdata['post_type'] === 'series') {
            // This will be converted to 'novel', so it's okay
        } else {
            $validation_errors[] = 'Invalid post type: ' . $postdata['post_type'];
        }
    }

    // Validate dates
    if (!empty($postdata['post_date']) && !ln_reader_validate_date($postdata['post_date'])) {
        $validation_errors[] = 'Invalid post date format';
    }

    // Log validation errors
    if (!empty($validation_errors)) {
        ln_reader_log_import_warning('Post validation errors: ' . implode(', ', $validation_errors), array(
            'post_title' => $postdata['post_title'] ?? 'Unknown',
            'post_type' => $postdata['post_type'] ?? 'Unknown'
        ));
    }

    return $postdata;
}

// Validate date format
function ln_reader_validate_date($date) {
    $d = DateTime::createFromFormat('Y-m-d H:i:s', $date);
    return $d && $d->format('Y-m-d H:i:s') === $date;
}

// Validate imported post after insertion
function ln_reader_validate_imported_post($post_id, $original_post_id, $postdata, $post) {
    $validation_issues = array();

    // Check if post was created successfully
    $imported_post = get_post($post_id);
    if (!$imported_post) {
        $validation_issues[] = 'Post was not created successfully';
        return;
    }

    // Validate post type specific requirements
    $post_type = get_post_type($post_id);

    if ($post_type === 'novel') {
        $validation_issues = array_merge($validation_issues, ln_reader_validate_novel_post($post_id));
    } elseif ($post_type === 'post') {
        $validation_issues = array_merge($validation_issues, ln_reader_validate_chapter_post($post_id));
    } elseif ($post_type === 'attachment') {
        $validation_issues = array_merge($validation_issues, ln_reader_validate_attachment_post($post_id));
    }

    // Log validation issues
    if (!empty($validation_issues)) {
        ln_reader_log_import_warning('Post validation issues: ' . implode(', ', $validation_issues), array(
            'post_id' => $post_id,
            'post_title' => get_the_title($post_id),
            'post_type' => $post_type
        ));
    }
}

// Validate novel post
function ln_reader_validate_novel_post($post_id) {
    $issues = array();

    // Check required meta fields
    $required_meta = array('_novel_status', '_author');
    foreach ($required_meta as $meta_key) {
        if (!get_post_meta($post_id, $meta_key, true)) {
            $issues[] = "Missing required meta field: $meta_key";
        }
    }

    // Check if novel has any genres
    $genres = wp_get_post_terms($post_id, 'novel_genre');
    if (empty($genres)) {
        $issues[] = 'Novel has no genres assigned';
    }

    return $issues;
}

// Validate chapter post
function ln_reader_validate_chapter_post($post_id) {
    $issues = array();

    // Check if chapter has novel relationship
    $novel_id = get_post_meta($post_id, '_novel_id', true);
    if (!$novel_id) {
        $issues[] = 'Chapter has no novel relationship';
    } elseif (!get_post($novel_id)) {
        $issues[] = 'Chapter linked to non-existent novel';
    }

    // Check chapter number
    $chapter_number = get_post_meta($post_id, '_chapter_number', true);
    if (!$chapter_number) {
        $issues[] = 'Chapter has no chapter number';
    }

    return $issues;
}

// Validate attachment post
function ln_reader_validate_attachment_post($post_id) {
    $issues = array();

    // Check if file exists
    $attached_file = get_post_meta($post_id, '_wp_attached_file', true);
    if ($attached_file) {
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['basedir'] . '/' . $attached_file;
        if (!file_exists($file_path)) {
            $issues[] = 'Attachment file does not exist on server';
        }
    } else {
        $issues[] = 'Attachment has no file reference';
    }

    return $issues;
}

// Post-import validation and integrity checks
function ln_reader_post_import_validation() {
    $validation_results = get_option('ln_reader_import_validation', array());
    $validation_results['post_import_checks'] = array();

    // Data integrity checks
    $integrity_check = ln_reader_validate_data_integrity();
    $validation_results['post_import_checks']['data_integrity'] = $integrity_check;

    // Relationship validation
    $relationship_check = ln_reader_validate_relationships();
    $validation_results['post_import_checks']['relationships'] = $relationship_check;

    // Content validation
    $content_check = ln_reader_validate_content_quality();
    $validation_results['post_import_checks']['content_quality'] = $content_check;

    // URL structure validation
    $url_check = ln_reader_validate_url_structure();
    $validation_results['post_import_checks']['url_structure'] = $url_check;

    // Media validation
    $media_check = ln_reader_validate_media_files();
    $validation_results['post_import_checks']['media_files'] = $media_check;

    update_option('ln_reader_import_validation', $validation_results);

    error_log('LN Reader Import: Post-import validation completed');
}

// Validate data integrity
function ln_reader_validate_data_integrity() {
    global $wpdb;

    $issues = array();

    // Check for orphaned chapters
    $orphaned_chapters = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->posts} p
        LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_novel_id'
        LEFT JOIN {$wpdb->posts} n ON pm.meta_value = n.ID AND n.post_type = 'novel'
        WHERE p.post_type = 'post'
        AND pm.meta_value IS NOT NULL
        AND n.ID IS NULL
    ");

    if ($orphaned_chapters > 0) {
        $issues[] = "$orphaned_chapters chapters linked to non-existent novels";
    }

    // Check for novels without categories
    $novels_without_categories = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->posts} p
        LEFT JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
        LEFT JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id AND tt.taxonomy = 'category'
        WHERE p.post_type = 'novel'
        AND p.post_status = 'publish'
        AND tt.term_id IS NULL
    ");

    if ($novels_without_categories > 0) {
        $issues[] = "$novels_without_categories novels without categories";
    }

    // Check for duplicate slugs
    $duplicate_slugs = $wpdb->get_var("
        SELECT COUNT(*)
        FROM (
            SELECT post_name, COUNT(*) as count
            FROM {$wpdb->posts}
            WHERE post_status = 'publish'
            GROUP BY post_name
            HAVING count > 1
        ) as duplicates
    ");

    if ($duplicate_slugs > 0) {
        $issues[] = "$duplicate_slugs duplicate post slugs found";
    }

    return array(
        'status' => empty($issues) ? 'pass' : 'warning',
        'issues' => $issues,
        'orphaned_chapters' => $orphaned_chapters,
        'novels_without_categories' => $novels_without_categories,
        'duplicate_slugs' => $duplicate_slugs
    );
}

// Validate relationships between posts
function ln_reader_validate_relationships() {
    $issues = array();

    // Get all novels
    $novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));

    $novels_with_chapters = 0;
    $novels_without_chapters = 0;

    foreach ($novels as $novel) {
        $chapters = get_posts(array(
            'post_type' => 'post',
            'meta_query' => array(
                array(
                    'key' => '_novel_id',
                    'value' => $novel->ID,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1
        ));

        if (empty($chapters)) {
            $novels_without_chapters++;
        } else {
            $novels_with_chapters++;
        }
    }

    if ($novels_without_chapters > 0) {
        $issues[] = "$novels_without_chapters novels have no chapters";
    }

    // Check for chapters with invalid novel IDs
    $chapters_with_invalid_novels = get_posts(array(
        'post_type' => 'post',
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'compare' => 'EXISTS'
            )
        ),
        'posts_per_page' => -1
    ));

    $invalid_relationships = 0;
    foreach ($chapters_with_invalid_novels as $chapter) {
        $novel_id = get_post_meta($chapter->ID, '_novel_id', true);
        if ($novel_id && !get_post($novel_id)) {
            $invalid_relationships++;
        }
    }

    if ($invalid_relationships > 0) {
        $issues[] = "$invalid_relationships chapters have invalid novel relationships";
    }

    return array(
        'status' => empty($issues) ? 'pass' : 'warning',
        'issues' => $issues,
        'novels_with_chapters' => $novels_with_chapters,
        'novels_without_chapters' => $novels_without_chapters,
        'invalid_relationships' => $invalid_relationships
    );
}

// Validate content quality
function ln_reader_validate_content_quality() {
    $issues = array();

    // Check for posts with very short content
    $short_content_posts = get_posts(array(
        'post_type' => array('post', 'novel'),
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => 'post_content_length',
                'value' => 100,
                'compare' => '<',
                'type' => 'NUMERIC'
            )
        )
    ));

    // Check for posts without titles
    global $wpdb;
    $posts_without_titles = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->posts}
        WHERE (post_title = '' OR post_title IS NULL)
        AND post_status = 'publish'
        AND post_type IN ('post', 'novel')
    ");

    if ($posts_without_titles > 0) {
        $issues[] = "$posts_without_titles posts without titles";
    }

    // Check for posts with default content
    $default_content_posts = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->posts}
        WHERE post_content LIKE '%Content imported from backup%'
        AND post_status = 'publish'
    ");

    if ($default_content_posts > 0) {
        $issues[] = "$default_content_posts posts with default import content";
    }

    return array(
        'status' => empty($issues) ? 'pass' : 'warning',
        'issues' => $issues,
        'posts_without_titles' => $posts_without_titles,
        'default_content_posts' => $default_content_posts
    );
}

// Validate URL structure
function ln_reader_validate_url_structure() {
    $issues = array();

    // Check for posts with problematic slugs
    global $wpdb;
    $problematic_slugs = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->posts}
        WHERE post_name REGEXP '[^a-z0-9\-]'
        AND post_status = 'publish'
    ");

    if ($problematic_slugs > 0) {
        $issues[] = "$problematic_slugs posts with non-standard URL slugs";
    }

    // Check for very long slugs
    $long_slugs = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->posts}
        WHERE LENGTH(post_name) > 200
        AND post_status = 'publish'
    ");

    if ($long_slugs > 0) {
        $issues[] = "$long_slugs posts with very long URL slugs";
    }

    return array(
        'status' => empty($issues) ? 'pass' : 'warning',
        'issues' => $issues,
        'problematic_slugs' => $problematic_slugs,
        'long_slugs' => $long_slugs
    );
}

// Validate media files
function ln_reader_validate_media_files() {
    $issues = array();

    // Get all attachments
    $attachments = get_posts(array(
        'post_type' => 'attachment',
        'posts_per_page' => -1,
        'post_status' => 'inherit'
    ));

    $missing_files = 0;
    $valid_files = 0;

    foreach ($attachments as $attachment) {
        $attached_file = get_post_meta($attachment->ID, '_wp_attached_file', true);
        if ($attached_file) {
            $upload_dir = wp_upload_dir();
            $file_path = $upload_dir['basedir'] . '/' . $attached_file;
            if (file_exists($file_path)) {
                $valid_files++;
            } else {
                $missing_files++;
            }
        } else {
            $missing_files++;
        }
    }

    if ($missing_files > 0) {
        $issues[] = "$missing_files attachment files are missing";
    }

    // Check for posts with missing featured images
    $posts_with_missing_thumbnails = 0;
    $posts_with_thumbnails = get_posts(array(
        'post_type' => array('post', 'novel'),
        'meta_query' => array(
            array(
                'key' => '_thumbnail_id',
                'compare' => 'EXISTS'
            )
        ),
        'posts_per_page' => -1
    ));

    foreach ($posts_with_thumbnails as $post) {
        $thumbnail_id = get_post_meta($post->ID, '_thumbnail_id', true);
        if (!get_post($thumbnail_id)) {
            $posts_with_missing_thumbnails++;
        }
    }

    if ($posts_with_missing_thumbnails > 0) {
        $issues[] = "$posts_with_missing_thumbnails posts reference missing featured images";
    }

    return array(
        'status' => empty($issues) ? 'pass' : 'warning',
        'issues' => $issues,
        'missing_files' => $missing_files,
        'valid_files' => $valid_files,
        'posts_with_missing_thumbnails' => $posts_with_missing_thumbnails
    );
}

// Import testing functions
function ln_reader_import_testing_functions() {
    // Add admin page for testing
    add_action('admin_menu', 'ln_reader_add_import_test_page');
}
add_action('init', 'ln_reader_import_testing_functions');

// Add import test admin page
function ln_reader_add_import_test_page() {
    add_submenu_page(
        'tools.php',
        'LN Reader Import Test',
        'Import Test',
        'manage_options',
        'ln-reader-import-test',
        'ln_reader_import_test_page'
    );
}

// Import test page
function ln_reader_import_test_page() {
    if (isset($_POST['run_test'])) {
        $test_results = ln_reader_run_import_tests();
    }

    echo '<div class="wrap">';
    echo '<h1>LN Reader Import Test</h1>';

    echo '<form method="post">';
    echo '<p>This will run a series of tests to verify the import compatibility of your theme.</p>';
    echo '<p><input type="submit" name="run_test" class="button-primary" value="Run Import Tests" /></p>';
    echo '</form>';

    if (isset($test_results)) {
        echo '<h2>Test Results</h2>';
        echo '<div class="test-results">';

        foreach ($test_results as $test_name => $result) {
            $status_class = $result['status'] === 'pass' ? 'notice-success' : ($result['status'] === 'warning' ? 'notice-warning' : 'notice-error');
            echo '<div class="notice ' . $status_class . '">';
            echo '<h3>' . esc_html($test_name) . '</h3>';
            echo '<p><strong>Status:</strong> ' . esc_html($result['status']) . '</p>';
            echo '<p><strong>Message:</strong> ' . esc_html($result['message']) . '</p>';
            if (!empty($result['details'])) {
                echo '<details><summary>Details</summary><pre>' . esc_html(print_r($result['details'], true)) . '</pre></details>';
            }
            echo '</div>';
        }

        echo '</div>';
    }

    echo '</div>';
}

// Run import tests
function ln_reader_run_import_tests() {
    $tests = array();

    // Test theme compatibility
    $tests['Theme Compatibility'] = ln_reader_validate_theme_compatibility();
    $tests['Theme Compatibility']['message'] = $tests['Theme Compatibility']['status'] === 'pass' ? 'Theme is fully compatible' : 'Theme has compatibility issues';

    // Test server requirements
    $tests['Server Requirements'] = ln_reader_validate_server_requirements();
    $tests['Server Requirements']['message'] = $tests['Server Requirements']['status'] === 'pass' ? 'Server meets all requirements' : 'Server has requirement issues';

    // Test database capacity
    $tests['Database Capacity'] = ln_reader_validate_database_capacity();

    // Test file permissions
    $tests['File Permissions'] = ln_reader_validate_file_permissions();

    // Test post type registration
    $tests['Post Type Registration'] = array(
        'status' => post_type_exists('novel') ? 'pass' : 'error',
        'message' => post_type_exists('novel') ? 'Novel post type is registered' : 'Novel post type is not registered',
        'details' => array(
            'novel_exists' => post_type_exists('novel'),
            'series_exists' => post_type_exists('series')
        )
    );

    // Test taxonomy registration
    $tests['Taxonomy Registration'] = array(
        'status' => (taxonomy_exists('novel_genre') && taxonomy_exists('novel_tag')) ? 'pass' : 'warning',
        'message' => (taxonomy_exists('novel_genre') && taxonomy_exists('novel_tag')) ? 'All taxonomies are registered' : 'Some taxonomies are missing',
        'details' => array(
            'novel_genre_exists' => taxonomy_exists('novel_genre'),
            'novel_tag_exists' => taxonomy_exists('novel_tag')
        )
    );

    return $tests;
}

// ========================================
// CUSTOM USER AUTHENTICATION & DASHBOARD SYSTEM
// ========================================

// Hide admin bar for non-admin users
function ln_reader_hide_admin_bar() {
    if (!current_user_can('administrator') && !is_admin()) {
        show_admin_bar(false);
    }
}
add_action('after_setup_theme', 'ln_reader_hide_admin_bar');

// Redirect non-admin users away from WordPress backend
function ln_reader_redirect_non_admin_users() {
    if (is_admin() && !current_user_can('administrator') && !wp_doing_ajax()) {
        wp_redirect(home_url('/dashboard'));
        exit;
    }
}
add_action('admin_init', 'ln_reader_redirect_non_admin_users');

// Custom login redirect
function ln_reader_login_redirect($redirect_to, $request, $user) {
    if (isset($user->roles) && is_array($user->roles)) {
        if (in_array('administrator', $user->roles)) {
            return admin_url();
        } else {
            return home_url('/dashboard');
        }
    }
    return $redirect_to;
}
add_filter('login_redirect', 'ln_reader_login_redirect', 10, 3);

// Custom logout redirect
function ln_reader_logout_redirect() {
    wp_redirect(home_url('/login'));
    exit;
}
add_action('wp_logout', 'ln_reader_logout_redirect');

// Enable user registration
function ln_reader_enable_registration() {
    update_option('users_can_register', 1);
    update_option('default_role', 'subscriber');

    // Log the settings change
    error_log('LN Reader: Registration enabled. users_can_register=' . get_option('users_can_register') . ', default_role=' . get_option('default_role'));
}
add_action('after_switch_theme', 'ln_reader_enable_registration');

// Force enable registration on every admin page load (temporary for debugging)
function ln_reader_force_enable_registration() {
    if (is_admin() && current_user_can('administrator')) {
        $current_setting = get_option('users_can_register');
        if (!$current_setting) {
            update_option('users_can_register', 1);
            error_log('LN Reader: Force enabled user registration');
        }

        $current_role = get_option('default_role');
        if ($current_role !== 'subscriber') {
            update_option('default_role', 'subscriber');
            error_log('LN Reader: Force set default role to subscriber');
        }
    }
}
add_action('admin_init', 'ln_reader_force_enable_registration');

// Create custom pages on theme activation
function ln_reader_create_custom_pages() {
    $pages = array(
        'login' => array(
            'title' => 'Login',
            'template' => 'page-login.php'
        ),
        'register' => array(
            'title' => 'Register',
            'template' => 'page-register.php'
        ),
        'dashboard' => array(
            'title' => 'Dashboard',
            'template' => 'page-dashboard.php'
        ),
        'reset-password' => array(
            'title' => 'Reset Password',
            'template' => 'page-reset-password.php'
        )
    );

    foreach ($pages as $slug => $page_data) {
        $existing_page = get_page_by_path($slug);
        if (!$existing_page) {
            $page_id = wp_insert_post(array(
                'post_title' => $page_data['title'],
                'post_name' => $slug,
                'post_content' => '',
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_author' => 1
            ));

            if ($page_id && !is_wp_error($page_id)) {
                update_post_meta($page_id, '_wp_page_template', $page_data['template']);
            }
        }
    }

    // Flush rewrite rules
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'ln_reader_create_custom_pages');

// Force create pages on admin init (temporary for debugging)
function ln_reader_force_create_pages() {
    if (is_admin() && current_user_can('administrator') && isset($_GET['create_auth_pages'])) {
        // Enable registration
        update_option('users_can_register', 1);
        update_option('default_role', 'subscriber');

        // Create pages
        ln_reader_create_custom_pages();

        // Add admin notice
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>Authentication pages created successfully!</strong></p>';
            echo '<p>Test URLs:</p>';
            echo '<ul>';
            echo '<li><a href="' . home_url('/login') . '" target="_blank">Login Page</a></li>';
            echo '<li><a href="' . home_url('/register') . '" target="_blank">Register Page</a></li>';
            echo '<li><a href="' . home_url('/dashboard') . '" target="_blank">Dashboard Page</a></li>';
            echo '</ul>';
            echo '</div>';
        });
    }
}
add_action('admin_init', 'ln_reader_force_create_pages');

// Add admin menu item for creating auth pages
function ln_reader_add_auth_admin_menu() {
    add_management_page(
        'Authentication Setup',
        'Auth Setup',
        'manage_options',
        'ln-reader-auth-setup',
        'ln_reader_auth_setup_page'
    );
}
add_action('admin_menu', 'ln_reader_add_auth_admin_menu');

function ln_reader_auth_setup_page() {
    echo '<div class="wrap">';
    echo '<h1>LN Reader Authentication Setup</h1>';

    if (isset($_GET['setup']) && $_GET['setup'] === 'run') {
        // Run setup
        update_option('users_can_register', 1);
        update_option('default_role', 'subscriber');
        ln_reader_create_custom_pages();
        flush_rewrite_rules();

        echo '<div class="notice notice-success"><p><strong>Setup completed!</strong></p></div>';
    }

    echo '<p>This tool will set up the custom authentication system for your LN Reader theme.</p>';
    echo '<p><a href="?page=ln-reader-auth-setup&setup=run" class="button button-primary">Run Authentication Setup</a></p>';

    // Show current status
    echo '<h2>Current Status</h2>';
    echo '<table class="widefat">';
    echo '<tr><th>Setting</th><th>Status</th></tr>';
    echo '<tr><td>User Registration</td><td>' . (get_option('users_can_register') ? '✓ Enabled' : '✗ Disabled') . '</td></tr>';
    echo '<tr><td>Default Role</td><td>' . get_option('default_role') . '</td></tr>';

    $pages = array('login', 'register', 'dashboard', 'reset-password');
    foreach ($pages as $slug) {
        $page = get_page_by_path($slug);
        $status = $page ? '✓ Exists (ID: ' . $page->ID . ')' : '✗ Missing';
        echo "<tr><td>{$slug} page</td><td>{$status}</td></tr>";
    }
    echo '</table>';

    echo '<h2>Test Links</h2>';
    echo '<ul>';
    echo '<li><a href="' . home_url('/login') . '" target="_blank">Login Page</a></li>';
    echo '<li><a href="' . home_url('/register') . '" target="_blank">Register Page</a></li>';
    echo '<li><a href="' . home_url('/dashboard') . '" target="_blank">Dashboard Page</a></li>';
    echo '<li><a href="' . home_url('/reset-password') . '" target="_blank">Reset Password Page</a></li>';
    echo '</ul>';

    echo '</div>';
}

// Handle custom login
function ln_reader_handle_login() {
    // Only process on login page
    if (!is_page('login')) {
        return;
    }

    if (isset($_POST['ln_reader_login_nonce']) && wp_verify_nonce($_POST['ln_reader_login_nonce'], 'ln_reader_login')) {
        $username = sanitize_text_field($_POST['username']);
        $password = $_POST['password'];
        $remember = isset($_POST['remember']);

        // Debug logging
        error_log('LN Reader Login Attempt: ' . $username);

        if (empty($username) || empty($password)) {
            wp_redirect(add_query_arg('login_error', urlencode('Please fill in all fields.'), home_url('/login')));
            exit;
        }

        $user = wp_authenticate($username, $password);

        if (is_wp_error($user)) {
            error_log('LN Reader Login Failed: ' . $user->get_error_message());
            wp_redirect(add_query_arg('login_error', urlencode($user->get_error_message()), home_url('/login')));
            exit;
        }

        // Success
        error_log('LN Reader Login Success: ' . $user->user_login);
        wp_clear_auth_cookie();
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID, $remember);

        // Redirect based on user role
        if (user_can($user, 'administrator')) {
            wp_redirect(admin_url());
        } else {
            wp_redirect(home_url('/dashboard'));
        }
        exit;
    }
}
add_action('template_redirect', 'ln_reader_handle_login');

// Handle custom registration
function ln_reader_handle_registration() {
    // Check if this is a POST request with our nonce
    if (!isset($_POST['ln_reader_register_nonce']) || !wp_verify_nonce($_POST['ln_reader_register_nonce'], 'ln_reader_register')) {
        return;
    }

    // Additional check: only process if we're on register page or if the form was submitted
    global $post;
    $is_register_page = (is_page('register') || (isset($post) && $post->post_name === 'register') ||
                        (isset($_POST['ln_reader_register_nonce']) && strpos($_SERVER['REQUEST_URI'], 'register') !== false));

    if (!$is_register_page) {
        error_log('LN Reader Registration: Not on register page. URI: ' . $_SERVER['REQUEST_URI']);
        return;
    }

    $username = sanitize_user($_POST['username']);
    $email = sanitize_email($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    error_log('LN Reader Registration Attempt: ' . $username . ' / ' . $email . ' on URI: ' . $_SERVER['REQUEST_URI']);

    $errors = array();

    // Validation
    if (empty($username) || empty($email) || empty($password) || empty($confirm_password)) {
        $errors[] = 'Please fill in all fields.';
    }

    if (!is_email($email)) {
        $errors[] = 'Please enter a valid email address.';
    }

    if (username_exists($username)) {
        $errors[] = 'Username already exists.';
    }

    if (email_exists($email)) {
        $errors[] = 'Email already registered.';
    }

    if (strlen($password) < 6) {
        $errors[] = 'Password must be at least 6 characters long.';
    }

    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match.';
    }

    if (!empty($errors)) {
        error_log('LN Reader Registration Validation Errors: ' . implode(', ', $errors));
        if (!session_id()) session_start();
        $_SESSION['registration_errors'] = $errors;
        wp_redirect(home_url('/register'));
        exit;
    }

    // Check if user registration is enabled
    if (!get_option('users_can_register')) {
        error_log('LN Reader Registration: User registration is disabled');
        if (!session_id()) session_start();
        $_SESSION['registration_errors'] = array('User registration is currently disabled.');
        wp_redirect(home_url('/register'));
        exit;
    }

    // Create user
    error_log('LN Reader Registration: Attempting to create user with wp_create_user()');
    $user_id = wp_create_user($username, $password, $email);

    if (is_wp_error($user_id)) {
        error_log('LN Reader Registration Failed: ' . $user_id->get_error_message());
        if (!session_id()) session_start();
        $_SESSION['registration_errors'] = array($user_id->get_error_message());
        wp_redirect(home_url('/register'));
        exit;
    }

    // Success - log detailed information
    error_log('LN Reader Registration Success: User ID ' . $user_id . ' created for ' . $username);

    // Set user role explicitly
    $user = new WP_User($user_id);
    $user->set_role('subscriber');
    error_log('LN Reader Registration: Set role to subscriber for user ' . $user_id);

    // Auto login after registration
    wp_clear_auth_cookie();
    wp_set_current_user($user_id);
    wp_set_auth_cookie($user_id);

    error_log('LN Reader Registration: Auto-login completed for user ' . $user_id);

    wp_redirect(home_url('/dashboard'));
    exit;
}
add_action('template_redirect', 'ln_reader_handle_registration');

// Handle password reset request
function ln_reader_handle_password_reset() {
    if (isset($_POST['ln_reader_reset_nonce']) && wp_verify_nonce($_POST['ln_reader_reset_nonce'], 'ln_reader_reset')) {
        $email = sanitize_email($_POST['email']);

        if (empty($email)) {
            wp_redirect(add_query_arg('reset_error', urlencode('Please enter your email address.'), home_url('/reset-password')));
            exit;
        }

        if (!is_email($email)) {
            wp_redirect(add_query_arg('reset_error', urlencode('Please enter a valid email address.'), home_url('/reset-password')));
            exit;
        }

        $user = get_user_by('email', $email);
        if (!$user) {
            wp_redirect(add_query_arg('reset_error', urlencode('No user found with that email address.'), home_url('/reset-password')));
            exit;
        }

        // Generate reset key
        $key = get_password_reset_key($user);
        if (is_wp_error($key)) {
            wp_redirect(add_query_arg('reset_error', urlencode('Unable to generate reset key.'), home_url('/reset-password')));
            exit;
        }

        // Send reset email
        $reset_url = add_query_arg(array(
            'action' => 'rp',
            'key' => $key,
            'login' => rawurlencode($user->user_login)
        ), home_url('/reset-password'));

        $subject = 'Password Reset Request';
        $message = "Hi {$user->display_name},\n\n";
        $message .= "You requested a password reset. Click the link below to reset your password:\n\n";
        $message .= $reset_url . "\n\n";
        $message .= "If you didn't request this, please ignore this email.\n\n";
        $message .= "Best regards,\n" . get_bloginfo('name');

        if (wp_mail($email, $subject, $message)) {
            wp_redirect(add_query_arg('reset_sent', '1', home_url('/reset-password')));
        } else {
            wp_redirect(add_query_arg('reset_error', urlencode('Unable to send reset email.'), home_url('/reset-password')));
        }
        exit;
    }
}
add_action('init', 'ln_reader_handle_password_reset');

// Handle new password submission
function ln_reader_handle_new_password() {
    if (isset($_POST['ln_reader_new_password_nonce']) && wp_verify_nonce($_POST['ln_reader_new_password_nonce'], 'ln_reader_new_password')) {
        $key = sanitize_text_field($_POST['key']);
        $login = sanitize_text_field($_POST['login']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];

        $user = check_password_reset_key($key, $login);
        if (is_wp_error($user)) {
            wp_redirect(add_query_arg('reset_error', urlencode('Invalid or expired reset link.'), home_url('/reset-password')));
            exit;
        }

        if (empty($password) || empty($confirm_password)) {
            $reset_url = add_query_arg(array('action' => 'rp', 'key' => $key, 'login' => rawurlencode($login)), home_url('/reset-password'));
            wp_redirect(add_query_arg('reset_error', urlencode('Please fill in all fields.'), $reset_url));
            exit;
        }

        if (strlen($password) < 6) {
            $reset_url = add_query_arg(array('action' => 'rp', 'key' => $key, 'login' => rawurlencode($login)), home_url('/reset-password'));
            wp_redirect(add_query_arg('reset_error', urlencode('Password must be at least 6 characters long.'), $reset_url));
            exit;
        }

        if ($password !== $confirm_password) {
            $reset_url = add_query_arg(array('action' => 'rp', 'key' => $key, 'login' => rawurlencode($login)), home_url('/reset-password'));
            wp_redirect(add_query_arg('reset_error', urlencode('Passwords do not match.'), $reset_url));
            exit;
        }

        // Reset password
        reset_password($user, $password);

        wp_redirect(add_query_arg('password_reset', '1', home_url('/login')));
        exit;
    }
}
add_action('init', 'ln_reader_handle_new_password');

// Handle password change from dashboard
function ln_reader_handle_password_change() {
    // Only process if we have the nonce and it's a POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['ln_reader_password_change_nonce'])) {
        return;
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['ln_reader_password_change_nonce'], 'ln_reader_password_change')) {
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_redirect(home_url('/login'));
        exit;
    }

    $current_password = sanitize_text_field($_POST['current_password']);
    $new_password = $_POST['new_password']; // Don't sanitize passwords
    $confirm_password = $_POST['confirm_password']; // Don't sanitize passwords

    $errors = array();
    $user = wp_get_current_user();

    // Validation
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $errors[] = 'Please fill in all fields.';
    }

    if (!empty($current_password) && !wp_check_password($current_password, $user->user_pass, $user->ID)) {
        $errors[] = 'Current password is incorrect.';
    }

    if (strlen($new_password) < 6) {
        $errors[] = 'New password must be at least 6 characters long.';
    }

    if ($new_password !== $confirm_password) {
        $errors[] = 'New passwords do not match.';
    }

    if (!empty($errors)) {
        if (!session_id()) session_start();
        $_SESSION['password_change_errors'] = $errors;
        wp_redirect(home_url('/dashboard#password'));
        exit;
    }

    // Update password
    error_log('LN Reader Password Change: Updating password for user ID: ' . $user->ID);

    // Store original hash for comparison
    $original_hash = $user->user_pass;
    error_log('LN Reader Password Change: Original hash: ' . substr($original_hash, 0, 20) . '...');

    // Update the password
    wp_set_password($new_password, $user->ID);

    // Verify password was updated by getting fresh user data
    wp_cache_delete($user->ID, 'users');
    $updated_user = get_user_by('id', $user->ID);
    $new_hash = $updated_user->user_pass;

    error_log('LN Reader Password Change: New hash: ' . substr($new_hash, 0, 20) . '...');
    error_log('LN Reader Password Change: Hash changed: ' . ($original_hash !== $new_hash ? 'YES' : 'NO'));

    // Test new password
    $new_password_works = wp_check_password($new_password, $new_hash, $user->ID);
    $old_password_fails = !wp_check_password($current_password, $new_hash, $user->ID);

    error_log('LN Reader Password Change: New password works: ' . ($new_password_works ? 'YES' : 'NO'));
    error_log('LN Reader Password Change: Old password fails: ' . ($old_password_fails ? 'YES' : 'NO'));

    if ($new_password_works && $old_password_fails) {
        error_log('LN Reader Password Change: Password update verified successfully');

        // Re-authenticate user with new password
        wp_clear_auth_cookie();
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID);

        error_log('LN Reader Password Change: User re-authenticated successfully');

        wp_redirect(add_query_arg('password_changed', '1', home_url('/dashboard#password')));
        exit;
    } else {
        error_log('LN Reader Password Change: Password update verification failed');
        if (!session_id()) session_start();
        $_SESSION['password_change_errors'] = array('Password update failed. Please try again.');
        wp_redirect(home_url('/dashboard#password'));
        exit;
    }
}
add_action('init', 'ln_reader_handle_password_change', 1);



// Handle profile update from dashboard
function ln_reader_handle_profile_update() {
    if (isset($_POST['ln_reader_profile_update_nonce']) && wp_verify_nonce($_POST['ln_reader_profile_update_nonce'], 'ln_reader_profile_update')) {
        if (!is_user_logged_in()) {
            wp_redirect(home_url('/login'));
            exit;
        }

        $display_name = sanitize_text_field($_POST['display_name']);
        $email = sanitize_email($_POST['email']);
        $bio = sanitize_textarea_field($_POST['bio']);

        $errors = array();
        $user_id = get_current_user_id();

        // Validation
        if (empty($display_name)) {
            $errors[] = 'Display name is required.';
        }

        if (empty($email) || !is_email($email)) {
            $errors[] = 'Please enter a valid email address.';
        }

        // Check if email is already used by another user
        $existing_user = get_user_by('email', $email);
        if ($existing_user && $existing_user->ID !== $user_id) {
            $errors[] = 'Email is already in use by another user.';
        }

        if (!empty($errors)) {
            if (!session_id()) session_start();
            $_SESSION['profile_update_errors'] = $errors;
            wp_redirect(home_url('/dashboard#profile'));
            exit;
        }

        // Update user data
        $user_data = array(
            'ID' => $user_id,
            'display_name' => $display_name,
            'user_email' => $email
        );

        $result = wp_update_user($user_data);

        if (is_wp_error($result)) {
            if (!session_id()) session_start();
            $_SESSION['profile_update_errors'] = array($result->get_error_message());
            wp_redirect(home_url('/dashboard#profile'));
            exit;
        }

        // Update bio
        update_user_meta($user_id, 'description', $bio);

        wp_redirect(add_query_arg('profile_updated', '1', home_url('/dashboard#profile')));
        exit;
    }
}
add_action('template_redirect', 'ln_reader_handle_profile_update');

// Reading history functions
function ln_reader_add_to_reading_history($user_id, $novel_id, $chapter_id) {
    $history = get_user_meta($user_id, 'reading_history', true);
    if (!is_array($history)) {
        $history = array();
    }

    // Remove existing entry for this novel
    foreach ($history as $key => $item) {
        if ($item['novel_id'] == $novel_id) {
            unset($history[$key]);
            break;
        }
    }

    // Add new entry at the beginning
    array_unshift($history, array(
        'novel_id' => $novel_id,
        'chapter_id' => $chapter_id,
        'timestamp' => current_time('timestamp')
    ));

    // Keep only last 50 entries
    $history = array_slice($history, 0, 50);

    update_user_meta($user_id, 'reading_history', $history);
}

function ln_reader_get_reading_history($user_id, $limit = 10) {
    $history = get_user_meta($user_id, 'reading_history', true);
    if (!is_array($history)) {
        return array();
    }

    $result = array();
    $count = 0;

    foreach ($history as $item) {
        if ($count >= $limit) break;

        $novel = get_post($item['novel_id']);
        $chapter = get_post($item['chapter_id']);

        if ($novel && $chapter) {
            $result[] = array(
                'novel' => $novel,
                'chapter' => $chapter,
                'timestamp' => $item['timestamp']
            );
            $count++;
        }
    }

    return $result;
}

// Auto-add to reading history when viewing a chapter
function ln_reader_auto_add_reading_history() {
    if (is_user_logged_in() && is_single()) {
        $post_id = get_the_ID();
        $novel_id = get_post_meta($post_id, '_novel_id', true);

        if ($novel_id) {
            $user_id = get_current_user_id();
            ln_reader_add_to_reading_history($user_id, $novel_id, $post_id);
        }
    }
}
add_action('wp_head', 'ln_reader_auto_add_reading_history');

// Handle bookmark removal from dashboard
function ln_reader_handle_bookmark_removal() {
    if (isset($_POST['ln_reader_remove_bookmark_nonce']) && wp_verify_nonce($_POST['ln_reader_remove_bookmark_nonce'], 'ln_reader_remove_bookmark')) {
        if (!is_user_logged_in()) {
            wp_redirect(home_url('/login'));
            exit;
        }

        $novel_id = intval($_POST['novel_id']);
        $user_id = get_current_user_id();

        $bookmarks = get_user_meta($user_id, 'bookmarked_novels', true);
        if (!is_array($bookmarks)) {
            $bookmarks = array();
        }

        // Remove from bookmarks
        $key = array_search($novel_id, $bookmarks);
        if ($key !== false) {
            unset($bookmarks[$key]);
            $bookmarks = array_values($bookmarks); // Re-index array
            update_user_meta($user_id, 'bookmarked_novels', $bookmarks);
        }

        wp_redirect(home_url('/dashboard#bookmarks'));
        exit;
    }
}
add_action('init', 'ln_reader_handle_bookmark_removal');

// Enqueue authentication styles and scripts
function ln_reader_enqueue_auth_assets() {
    // Enqueue auth CSS on authentication pages
    if (is_page(array('login', 'register', 'reset-password', 'dashboard'))) {
        wp_enqueue_style('ln-reader-auth', get_template_directory_uri() . '/css/auth.css', array(), '1.0.0');
        wp_enqueue_script('ln-reader-auth', get_template_directory_uri() . '/js/auth.js', array('jquery'), '1.0.0', true);

        // Add nonce for AJAX requests
        wp_localize_script('ln-reader-auth', 'ln_reader_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ln_reader_nonce'),
            'bookmark_nonce' => wp_create_nonce('bookmark_nonce')
        ));
    }
}
add_action('wp_enqueue_scripts', 'ln_reader_enqueue_auth_assets');

// Redirect wp-login.php to custom login page
function ln_reader_redirect_login_page() {
    $page_viewed = basename($_SERVER['REQUEST_URI']);

    if ($page_viewed == "wp-login.php" && $_SERVER['REQUEST_METHOD'] == 'GET') {
        $redirect_to = isset($_REQUEST['redirect_to']) ? $_REQUEST['redirect_to'] : null;

        if (isset($_REQUEST['action'])) {
            $action = $_REQUEST['action'];

            if ($action == 'register') {
                wp_redirect(home_url('/register'));
                exit;
            } elseif ($action == 'lostpassword' || $action == 'rp') {
                wp_redirect(home_url('/reset-password'));
                exit;
            }
        }

        if ($redirect_to) {
            wp_redirect(add_query_arg('redirect_to', urlencode($redirect_to), home_url('/login')));
        } else {
            wp_redirect(home_url('/login'));
        }
        exit;
    }
}
add_action('init', 'ln_reader_redirect_login_page');

// Disable WordPress default login/register URLs for non-admins
function ln_reader_disable_default_auth_pages() {
    global $pagenow;

    if ($pagenow === 'wp-login.php' && $_SERVER['REQUEST_METHOD'] == 'POST') {
        // Allow POST requests to wp-login.php for logout and other necessary functions
        return;
    }

    if ($pagenow === 'wp-login.php' && !current_user_can('administrator')) {
        ln_reader_redirect_login_page();
    }
}
add_action('init', 'ln_reader_disable_default_auth_pages');

// Start session di WordPress
function start_session() {
    if (!session_id()) {
        session_start();
    }
}
add_action('init', 'start_session', 1);

// Hook ke wp_head untuk menghitung views
add_action('wp_head', 'set_post_views');

// Fungsi untuk mendapatkan jumlah views
function get_post_views($post_id) {
    $count = get_post_meta($post_id, 'post_views', true);

    if ($count == '') {
        delete_post_meta($post_id, 'post_views');
        add_post_meta($post_id, 'post_views', '0');
        return "0";
    }

    return $count;
}

// Enhanced WordPress XML Import Handler for OssanTL Data
function ln_reader_handle_ossantl_import() {
    // Hook into WordPress import process
    add_action('import_start', 'ln_reader_prepare_ossantl_import');
    add_action('import_end', 'ln_reader_finalize_ossantl_import');
    // Note: Meta processing is handled by ln_reader_comprehensive_meta_mapping
    add_action('wp_import_insert_post', 'ln_reader_process_ossantl_post', 10, 4);
    // Add post-import validation
    add_action('wp_import_insert_post', 'ln_reader_validate_chapter_after_import', 20, 1);
}
add_action('init', 'ln_reader_handle_ossantl_import');

// Prepare for OssanTL import
function ln_reader_prepare_ossantl_import() {
    error_log('LN Reader: Starting OssanTL XML import process');

    // Temporarily increase memory and time limits
    ini_set('memory_limit', '512M');
    set_time_limit(300);

    // Store import start time
    update_option('ln_reader_import_start_time', current_time('timestamp'));
}

// Process OssanTL meta fields during import
function ln_reader_process_ossantl_meta($meta, $post_id, $post) {
    if (empty($meta)) {
        return $meta;
    }

    $processed_meta = array();

    foreach ($meta as $meta_item) {
        $key = $meta_item['key'];
        $value = $meta_item['value'];

        // Handle OssanTL specific meta fields
        switch ($key) {
            case 'ero_chapter':
                // Map to theme's chapter number field
                $processed_meta[] = array('key' => '_chapter_number', 'value' => $value);
                error_log("LN Reader: Mapped ero_chapter '$value' to _chapter_number for post $post_id");
                break;

            case 'ero_title':
                // Map to chapter title
                $processed_meta[] = array('key' => '_chapter_title', 'value' => $value);
                break;

            case 'ero_volume':
                // Map to volume number
                $processed_meta[] = array('key' => '_volume_number', 'value' => $value);
                break;

            case 'ero_latestid':
                // Store latest chapter info for novels
                $processed_meta[] = array('key' => '_latest_chapters', 'value' => $value);
                break;

            case 'wpb_post_views_count':
            case 'post_views':
                // Map view counts
                $processed_meta[] = array('key' => '_view_count', 'value' => intval($value));
                $processed_meta[] = array('key' => 'post_views', 'value' => intval($value));
                break;

            default:
                // Keep original meta
                $processed_meta[] = $meta_item;
                break;
        }
    }

    return $processed_meta;
}

// Process posts after import
function ln_reader_process_ossantl_post($post_id, $original_post_ID, $postdata, $post) {
    error_log("LN Reader: Processing post $post_id, type: " . $post['post_type']);

    // Skip if not a regular post
    if ($post['post_type'] !== 'post') {
        return;
    }

    // Check if this post has chapter number (indicating it's a chapter)
    $chapter_number = get_post_meta($post_id, '_chapter_number', true);
    error_log("LN Reader: Post $post_id chapter number: " . ($chapter_number ?: 'NONE'));

    if (!empty($chapter_number)) {
        // This is a chapter post
        ln_reader_process_chapter_import($post_id, $post);
    } else {
        // Try to process as chapter anyway based on title pattern
        $title = get_the_title($post_id);
        if (preg_match('/chapter\s+(\d+)/i', $title, $matches)) {
            $extracted_number = intval($matches[1]);
            update_post_meta($post_id, '_chapter_number', $extracted_number);
            error_log("LN Reader: Extracted chapter number $extracted_number from title: $title");
            ln_reader_process_chapter_import($post_id, $post);
        }
    }
}

// Enhanced post-import validation for chapters
function ln_reader_validate_chapter_after_import($post_id) {
    $post_type = get_post_type($post_id);
    if ($post_type !== 'post') {
        return;
    }

    $title = get_the_title($post_id);
    $chapter_number = get_post_meta($post_id, '_chapter_number', true);

    // If no chapter number is set, try to extract it from title or other meta
    if (empty($chapter_number)) {
        // Try to extract from title
        $extracted_number = ln_reader_extract_chapter_number_from_title($title);
        if ($extracted_number) {
            update_post_meta($post_id, '_chapter_number', $extracted_number);
            error_log("LN Reader: Post-import validation - Set chapter number $extracted_number for post $post_id");
        } else {
            // Check if this might be a chapter based on other indicators
            $import_series_id = get_post_meta($post_id, '_import_series_id', true);
            if ($import_series_id) {
                // This looks like a chapter, log it for manual review
                error_log("LN Reader: Post-import validation - Post $post_id appears to be a chapter but has no chapter number. Title: '$title'");

                // Write to error log for tracking
                $error_message = "Post validation issues: Chapter has no chapter number";
                $error_data = json_encode(array(
                    'post_id' => $post_id,
                    'post_title' => $title,
                    'post_type' => $post_type
                ));
                $timestamp = current_time('Y-m-d H:i:s');

                $log_entry = "$error_message\t$error_data\t$timestamp\n";
                file_put_contents(get_template_directory() . '/error_import.txt', $log_entry, FILE_APPEND | LOCK_EX);
            }
        }
    }

    // Validate novel relationship
    $novel_id = get_post_meta($post_id, '_novel_id', true);
    if (empty($novel_id) && !empty($chapter_number)) {
        // This is a chapter without a novel relationship
        error_log("LN Reader: Post-import validation - Chapter $post_id has no novel relationship");

        $error_message = "Post validation issues: Chapter has no novel relationship";
        $error_data = json_encode(array(
            'post_id' => $post_id,
            'post_title' => $title,
            'post_type' => $post_type
        ));
        $timestamp = current_time('Y-m-d H:i:s');

        $log_entry = "$error_message\t$error_data\t$timestamp\n";
        file_put_contents(get_template_directory() . '/error_import.txt', $log_entry, FILE_APPEND | LOCK_EX);
    }
}

// Process chapter-specific import logic
function ln_reader_process_chapter_import($post_id, $post_data) {
    $current_title = get_the_title($post_id);
    error_log("LN Reader: Processing chapter import for post $post_id with title: '$current_title'");

    // Get or extract chapter number
    $chapter_number = get_post_meta($post_id, '_chapter_number', true);

    // If no chapter number, try to extract from title
    if (empty($chapter_number)) {
        $chapter_number = ln_reader_extract_chapter_number_from_title($current_title);
        if ($chapter_number) {
            update_post_meta($post_id, '_chapter_number', $chapter_number);
            error_log("LN Reader: Extracted chapter number $chapter_number from title for post $post_id");
        }
    }

    // Try to find the parent novel based on categories (skip uncategorized)
    $categories = wp_get_post_categories($post_id);
    $novel_id = null;
    $valid_category = null;

    foreach ($categories as $cat_id) {
        $category = get_category($cat_id);
        if ($category && $category->slug !== 'uncategorized') {
            $valid_category = $category;

            // Get import series ID to check if series exists
            $import_series_id = get_post_meta($post_id, '_import_series_id', true);

            // Use centralized function to find or create novel
            $novel_result = ln_reader_find_or_create_novel($category->name, $import_series_id);

            if ($novel_result === 'SERIES_EXISTS') {
                // A series exists that will be converted to this novel, skip creation for now
                error_log("LN Reader: Skipping novel creation for '{$category->name}' - series will be converted later");
                break;
            } elseif (!$novel_result) {
                // Novel doesn't exist, create it
                $novel_id = ln_reader_create_novel_from_category($category, $post_id);
                if ($novel_id) {
                    error_log("LN Reader: Created new novel $novel_id for category '{$category->name}'");
                }
            } else {
                $novel_id = $novel_result;
                error_log("LN Reader: Found existing novel $novel_id for category '{$category->name}'");
            }

            if ($novel_id) {
                break;
            }
        }
    }

    // If no valid category found, try to determine novel from title pattern
    if (!$novel_id && !$valid_category) {
        $novel_info = ln_reader_determine_novel_from_title($current_title);
        if ($novel_info) {
            error_log("LN Reader: Determined novel from title pattern: '{$novel_info['title']}'");

            // Find or create the category
            $category = get_term_by('name', $novel_info['title'], 'category');
            if (!$category) {
                $category_result = wp_insert_term(
                    $novel_info['title'],
                    'category',
                    array(
                        'description' => 'Category for novel: ' . $novel_info['title'],
                        'slug' => sanitize_title($novel_info['title'])
                    )
                );

                if (!is_wp_error($category_result)) {
                    $category = get_term($category_result['term_id'], 'category');
                    error_log("LN Reader: Created category '{$novel_info['title']}' for post $post_id");
                }
            }

            if ($category && !is_wp_error($category)) {
                // Assign chapter to the correct category
                wp_set_post_categories($post_id, array($category->term_id));
                error_log("LN Reader: Assigned post $post_id to category '{$category->name}'");

                // Get import series ID to check if series exists
                $import_series_id = get_post_meta($post_id, '_import_series_id', true);

                // Find or create novel using centralized function
                $novel_result = ln_reader_find_or_create_novel($novel_info['title'], $import_series_id);

                if ($novel_result === 'SERIES_EXISTS') {
                    // A series exists that will be converted to this novel, skip creation for now
                    error_log("LN Reader: Skipping novel creation for '{$novel_info['title']}' - series will be converted later");
                } elseif (!$novel_result) {
                    // Novel doesn't exist, create it
                    $novel_id = ln_reader_create_novel_from_category($category, $post_id);
                } else {
                    $novel_id = $novel_result;
                }
            }
        }
    }

    // Link chapter to novel
    if ($novel_id) {
        update_post_meta($post_id, '_novel_id', $novel_id);
        error_log("LN Reader: Linked chapter $post_id (Chapter $chapter_number) to novel $novel_id");

        // Update novel's chapter count
        ln_reader_update_novel_chapter_count($novel_id);

        // Set proper post title
        ln_reader_set_proper_chapter_title($post_id, $novel_id, $chapter_number);
    } else {
        error_log("LN Reader: WARNING - Could not link chapter $post_id to any novel. Title: '$current_title'");

        // Even without a novel, try to fix the title if we have a chapter number
        if ($chapter_number) {
            ln_reader_fix_orphaned_chapter_title($post_id, $current_title, $chapter_number);
        }
    }
}

// Extract chapter number from title using various patterns
function ln_reader_extract_chapter_number_from_title($title) {
    // Try various patterns to extract chapter number
    $patterns = array(
        '/chapter\s*(\d+(?:\.\d+)?)/i',
        '/episode\s*(\d+(?:\.\d+)?)/i',
        '/ch\s*(\d+(?:\.\d+)?)/i',
        '/ep\s*(\d+(?:\.\d+)?)/i',
        '/^(\d+(?:\.\d+)?)$/',  // Just a number
        '/\s(\d+(?:\.\d+)?)$/'   // Number at the end
    );

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $title, $matches)) {
            return floatval($matches[1]);
        }
    }

    return null;
}

// Determine novel information from chapter title using patterns
function ln_reader_determine_novel_from_title($title) {
    $title_lower = strtolower($title);

    // Define patterns for known novels
    $novel_patterns = array(
        array(
            'pattern' => '/ossan|arafoo/i',
            'title' => 'Arafoo Kenja no Isekai Seikatsu Nikki',
            'author' => 'Unknown Author'
        ),
        array(
            'pattern' => '/flower pot|demon king/i',
            'title' => 'FLOWER POT MAN~I was just taming parasitic plants and modifying them into demonic hybrids, before I knew it, people started calling me the Demon King~',
            'author' => 'Unknown Author'
        ),
        array(
            'pattern' => '/villainess|brother/i',
            'title' => 'I Was Reincarnated as the Villainess\'s Brother, but I Couldn\'t Use Magic, So I\'ll Slash the Destruction Flag with My Sword!',
            'author' => 'Unknown Author'
        ),
        array(
            'pattern' => '/seijo|saint/i',
            'title' => 'Seijo wa tokkuni shōkan sa rete iru. Nihon ni',
            'author' => 'Unknown Author'
        ),
        array(
            'pattern' => '/powers of the gods|ability score/i',
            'title' => 'The One Who Wields the Powers of the Gods ~ I\'m Looked Down Upon for My Ability Score of \'0\', but I\'m Actually One of the Strongest in the World.',
            'author' => 'Unknown Author'
        )
    );

    foreach ($novel_patterns as $pattern) {
        if (preg_match($pattern['pattern'], $title)) {
            return array(
                'title' => $pattern['title'],
                'author' => $pattern['author']
            );
        }
    }

    // If no pattern matches, try to extract from chapter title format
    if (preg_match('/^(.+?)\s+(?:chapter|episode|ch|ep)\s+\d+/i', $title, $matches)) {
        $novel_title = trim($matches[1]);
        if (strlen($novel_title) > 3) { // Reasonable novel title length
            return array(
                'title' => $novel_title,
                'author' => 'Unknown Author'
            );
        }
    }

    return null;
}

// Set proper chapter title based on novel and chapter number
function ln_reader_set_proper_chapter_title($post_id, $novel_id, $chapter_number) {
    if (!$novel_id || !$chapter_number) {
        return;
    }

    $novel = get_post($novel_id);
    if (!$novel) {
        return;
    }

    $current_title = get_the_title($post_id);

    // Don't modify titles that already look correct
    if (strpos($current_title, $novel->post_title) !== false &&
        (strpos($current_title, "Chapter $chapter_number") !== false ||
         strpos($current_title, "Episode $chapter_number") !== false)) {
        return; // Title already looks good
    }

    // Only fix titles that are clearly corrupted (just numbers or very short)
    if (strlen(trim($current_title)) > 10 && !is_numeric(trim($current_title))) {
        // Title seems reasonable, don't change it unless it's clearly wrong
        if (!preg_match('/^\d+(\.\d+)?$/', trim($current_title))) {
            return;
        }
    }

    $volume = get_post_meta($post_id, '_volume_number', true);
    $chapter_title = get_post_meta($post_id, '_chapter_title', true);

    $title_parts = array($novel->post_title);

    if (!empty($volume)) {
        // Check if volume already contains "Volume" text
        if (stripos($volume, 'volume') !== false) {
            $title_parts[] = $volume;
        } else {
            $title_parts[] = "Volume $volume";
        }
    }

    $title_parts[] = "Chapter $chapter_number";

    // Add chapter title if available
    if (!empty($chapter_title)) {
        $title_parts[] = $chapter_title;
    }

    $new_title = implode(' - ', $title_parts);

    if ($current_title !== $new_title) {
        wp_update_post(array(
            'ID' => $post_id,
            'post_title' => $new_title
        ));

        error_log("LN Reader: Updated chapter title for post $post_id: '$new_title' (was: '$current_title')");
    }
}

// Fix title for orphaned chapters (chapters without novel relationship)
function ln_reader_fix_orphaned_chapter_title($post_id, $current_title, $chapter_number) {
    // If the current title contains "uncategorized", try to fix it
    if (stripos($current_title, 'uncategorized') !== false) {
        // Try to extract novel name from the title
        $novel_info = ln_reader_determine_novel_from_title($current_title);

        if ($novel_info) {
            $new_title = $novel_info['title'] . " - Chapter $chapter_number";
        } else {
            // Generic fallback
            $new_title = "Chapter $chapter_number";
        }

        wp_update_post(array(
            'ID' => $post_id,
            'post_title' => $new_title
        ));

        error_log("LN Reader: Fixed orphaned chapter title for post $post_id: '$new_title' (was: '$current_title')");
    }
}

// Create novel from category during import with duplicate prevention
function ln_reader_create_novel_from_category($category, $sample_post_id = null) {
    // First check if novel already exists
    $existing_novel = ln_reader_find_or_create_novel($category->name);
    if ($existing_novel) {
        error_log("LN Reader: Found existing novel {$existing_novel} for category '{$category->name}'");
        return $existing_novel;
    }

    // Create novel post
    $novel_data = array(
        'post_title' => $category->name,
        'post_type' => 'novel',
        'post_status' => 'publish',
        'post_content' => $category->description ?: 'Novel imported from OssanTL.',
    );

    $novel_id = wp_insert_post($novel_data);

    if (is_wp_error($novel_id)) {
        error_log("LN Reader: Failed to create novel for category {$category->name}");
        return null;
    }

    // Copy taxonomies from sample post if available
    if ($sample_post_id) {
        // Copy writer taxonomy
        $writers = wp_get_post_terms($sample_post_id, 'writer');
        if (!empty($writers) && !is_wp_error($writers)) {
            $writer_ids = wp_list_pluck($writers, 'term_id');
            wp_set_post_terms($novel_id, $writer_ids, 'writer');

            // Set author meta from writer taxonomy
            $writer_names = wp_list_pluck($writers, 'name');
            $author_string = implode(', ', $writer_names);
            update_post_meta($novel_id, '_author', $author_string);
            update_post_meta($novel_id, '_novel_author', $author_string);
        }

        // Copy other taxonomies
        $taxonomies = array('genre', 'type', 'artist');
        foreach ($taxonomies as $taxonomy) {
            if (taxonomy_exists($taxonomy)) {
                $terms = wp_get_post_terms($sample_post_id, $taxonomy);
                if (!empty($terms) && !is_wp_error($terms)) {
                    $term_ids = wp_list_pluck($terms, 'term_id');
                    wp_set_post_terms($novel_id, $term_ids, $taxonomy);
                }
            }
        }

        // Copy featured image if available
        $thumbnail_id = get_post_thumbnail_id($sample_post_id);
        if ($thumbnail_id) {
            set_post_thumbnail($novel_id, $thumbnail_id);
        }
    }

    error_log("LN Reader: Created novel $novel_id for category {$category->name}");
    return $novel_id;
}

// Update novel chapter count
function ln_reader_update_novel_chapter_count($novel_id) {
    $chapters = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'value' => $novel_id,
                'compare' => '='
            )
        ),
        'fields' => 'ids'
    ));

    $chapter_count = count($chapters);
    update_post_meta($novel_id, '_chapter_count', $chapter_count);

    // Get latest chapter
    if (!empty($chapters)) {
        $latest_chapters = get_posts(array(
            'post_type' => 'post',
            'posts_per_page' => 1,
            'meta_query' => array(
                array(
                    'key' => '_novel_id',
                    'value' => $novel_id,
                    'compare' => '='
                )
            ),
            'meta_key' => '_chapter_number',
            'orderby' => 'meta_value_num',
            'order' => 'DESC'
        ));

        if (!empty($latest_chapters)) {
            $latest_chapter = $latest_chapters[0];
            $latest_chapter_number = get_post_meta($latest_chapter->ID, '_chapter_number', true);
            update_post_meta($novel_id, '_latest_chapter', $latest_chapter_number);
            update_post_meta($novel_id, '_latest_chapter_id', $latest_chapter->ID);
        }
    }
}

// Comprehensive post-import validation and fixes
function ln_reader_comprehensive_post_import_validation() {
    error_log('LN Reader: Starting comprehensive post-import validation');

    $validation_results = array(
        'chapters_fixed' => 0,
        'categories_fixed' => 0,
        'novels_created' => 0,
        'relationships_fixed' => 0,
        'titles_fixed' => 0
    );

    // 1. Fix chapters without chapter numbers
    $posts_without_numbers = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_chapter_number',
                'compare' => 'NOT EXISTS'
            )
        )
    ));

    foreach ($posts_without_numbers as $post) {
        $chapter_number = ln_reader_extract_chapter_number_from_title($post->post_title);
        if ($chapter_number) {
            update_post_meta($post->ID, '_chapter_number', $chapter_number);
            $validation_results['chapters_fixed']++;
            error_log("LN Reader: Validation - Added chapter number $chapter_number to post {$post->ID}");
        }
    }

    // 2. Fix uncategorized chapters
    $uncategorized_chapters = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => -1,
        'category' => 1, // Uncategorized category ID
        'meta_query' => array(
            array(
                'key' => '_chapter_number',
                'compare' => 'EXISTS'
            )
        )
    ));

    foreach ($uncategorized_chapters as $chapter) {
        $novel_info = ln_reader_determine_novel_from_title($chapter->post_title);
        if ($novel_info) {
            // Find or create category
            $category = get_term_by('name', $novel_info['title'], 'category');
            if (!$category) {
                $category_result = wp_insert_term(
                    $novel_info['title'],
                    'category',
                    array(
                        'description' => 'Category for novel: ' . $novel_info['title'],
                        'slug' => sanitize_title($novel_info['title'])
                    )
                );

                if (!is_wp_error($category_result)) {
                    $category = get_term($category_result['term_id'], 'category');
                    $validation_results['categories_fixed']++;
                }
            }

            if ($category && !is_wp_error($category)) {
                wp_set_post_categories($chapter->ID, array($category->term_id));
                error_log("LN Reader: Validation - Fixed category for chapter {$chapter->ID}");
            }
        }
    }

    // 3. Fix chapters without novel relationships
    $orphaned_chapters = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_chapter_number',
                'compare' => 'EXISTS'
            ),
            array(
                'key' => '_novel_id',
                'compare' => 'NOT EXISTS'
            )
        )
    ));

    foreach ($orphaned_chapters as $chapter) {
        $categories = wp_get_post_categories($chapter->ID);
        foreach ($categories as $cat_id) {
            $category = get_category($cat_id);
            if ($category && $category->slug !== 'uncategorized') {
                // Find or create novel for this category using centralized function
                $novel_id = ln_reader_find_or_create_novel($category->name);

                if (!$novel_id) {
                    // Create novel
                    $novel_id = ln_reader_create_novel_from_category($category, $chapter->ID);
                    if ($novel_id) {
                        $validation_results['novels_created']++;
                    }
                }

                if ($novel_id) {
                    update_post_meta($chapter->ID, '_novel_id', $novel_id);
                    $validation_results['relationships_fixed']++;
                    error_log("LN Reader: Validation - Linked chapter {$chapter->ID} to novel $novel_id");

                    // Fix chapter title
                    $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                    if ($chapter_number) {
                        ln_reader_set_proper_chapter_title($chapter->ID, $novel_id, $chapter_number);
                        $validation_results['titles_fixed']++;
                    }
                }
                break;
            }
        }
    }

    error_log('LN Reader: Post-import validation completed: ' . json_encode($validation_results));
    update_option('ln_reader_validation_results', $validation_results);
}

// Finalize OssanTL import
function ln_reader_finalize_ossantl_import() {
    error_log('LN Reader: Finalizing OssanTL XML import process');

    // Run comprehensive validation first
    ln_reader_comprehensive_post_import_validation();

    // Clean up and optimize
    ln_reader_cleanup_import_data();

    // Update novel statistics
    ln_reader_update_all_novel_stats();

    // Flush rewrite rules
    flush_rewrite_rules();

    // Store import completion time
    update_option('ln_reader_import_end_time', current_time('timestamp'));

    $start_time = get_option('ln_reader_import_start_time', 0);
    $end_time = current_time('timestamp');
    $duration = $end_time - $start_time;

    error_log("LN Reader: Import completed in {$duration} seconds");
}

// Clean up import data
function ln_reader_cleanup_import_data() {
    // Remove orphaned meta
    global $wpdb;

    $wpdb->query("
        DELETE pm FROM {$wpdb->postmeta} pm
        LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
        WHERE p.ID IS NULL
    ");

    // Clean up empty terms
    $wpdb->query("
        DELETE t FROM {$wpdb->terms} t
        LEFT JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id
        WHERE tt.term_id IS NULL
    ");
}

// Update all novel statistics
function ln_reader_update_all_novel_stats() {
    $novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'post_status' => 'any',
        'fields' => 'ids'
    ));

    foreach ($novels as $novel_id) {
        ln_reader_update_novel_chapter_count($novel_id);
    }

    error_log("LN Reader: Updated statistics for " . count($novels) . " novels");
}

// Map imported taxonomies to theme taxonomies
function ln_reader_map_imported_taxonomies() {
    // Map 'genre' taxonomy to 'novel_genre' for novels
    $genre_terms = get_terms(array(
        'taxonomy' => 'genre',
        'hide_empty' => false
    ));

    if (!empty($genre_terms) && !is_wp_error($genre_terms)) {
        foreach ($genre_terms as $genre_term) {
            // Check if equivalent term exists in novel_genre
            $existing_term = get_term_by('name', $genre_term->name, 'novel_genre');

            if (!$existing_term) {
                // Create term in novel_genre taxonomy
                $new_term = wp_insert_term(
                    $genre_term->name,
                    'novel_genre',
                    array(
                        'description' => $genre_term->description,
                        'slug' => $genre_term->slug
                    )
                );

                if (!is_wp_error($new_term)) {
                    error_log("LN Reader: Created novel_genre term '{$genre_term->name}'");
                }
            }
        }
    }

    // Now assign novel_genre terms to novels based on their genre terms
    $novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'post_status' => 'any'
    ));

    foreach ($novels as $novel) {
        $genre_terms = wp_get_post_terms($novel->ID, 'genre');

        if (!empty($genre_terms) && !is_wp_error($genre_terms)) {
            $novel_genre_ids = array();

            foreach ($genre_terms as $genre_term) {
                $novel_genre_term = get_term_by('name', $genre_term->name, 'novel_genre');
                if ($novel_genre_term) {
                    $novel_genre_ids[] = $novel_genre_term->term_id;
                }
            }

            if (!empty($novel_genre_ids)) {
                wp_set_post_terms($novel->ID, $novel_genre_ids, 'novel_genre');
                error_log("LN Reader: Mapped genres for novel {$novel->ID}");
            }
        }
    }
}

// Run taxonomy mapping after import
add_action('import_end', 'ln_reader_map_imported_taxonomies', 20);

// Note: Custom permalink setup removed - using WordPress default permalink handling

// Note: Chapter post slug fix function removed - using WordPress default slug handling

// Note: Permalink fix admin notice removed

// Add admin notice for import instructions
function ln_reader_import_admin_notice() {
    $screen = get_current_screen();

    if ($screen && $screen->id === 'tools_page_wordpress-importer') {
        ?>
        <div class="notice notice-info">
            <h3><?php _e('LN Reader Theme - OssanTL Import Ready', 'ln-reader'); ?></h3>
            <p><?php _e('Your LN Reader theme is configured to handle OssanTL XML imports. The following will be automatically processed:', 'ln-reader'); ?></p>
            <ul style="list-style: disc; margin-left: 20px;">
                <li><?php _e('Chapter numbers (ero_chapter → _chapter_number)', 'ln-reader'); ?></li>
                <li><?php _e('Writer taxonomy → Author meta fields', 'ln-reader'); ?></li>
                <li><?php _e('Categories → Novel creation', 'ln-reader'); ?></li>
                <li><?php _e('Genre mapping to theme taxonomies', 'ln-reader'); ?></li>
                <li><?php _e('Standard WordPress permalink handling', 'ln-reader'); ?></li>
            </ul>
            <p><strong><?php _e('Note:', 'ln-reader'); ?></strong> <?php _e('Large imports may take several minutes. Please be patient and do not refresh the page.', 'ln-reader'); ?></p>
        </div>
        <?php
    }
}
add_action('admin_notices', 'ln_reader_import_admin_notice');

// Add import status to admin dashboard
function ln_reader_import_status_widget() {
    $last_import = get_option('ln_reader_import_end_time', 0);

    if ($last_import) {
        $import_date = date('Y-m-d H:i:s', $last_import);

        // Count imported content
        $novels_count = wp_count_posts('novel')->publish;
        $chapters_count = get_posts(array(
            'post_type' => 'post',
            'meta_key' => '_chapter_number',
            'posts_per_page' => -1,
            'fields' => 'ids'
        ));
        $chapters_count = count($chapters_count);

        ?>
        <div class="wrap">
            <h3><?php _e('Import Status', 'ln-reader'); ?></h3>
            <p><?php printf(__('Last import: %s', 'ln-reader'), $import_date); ?></p>
            <p><?php printf(__('Novels: %d | Chapters: %d', 'ln-reader'), $novels_count, $chapters_count); ?></p>
        </div>
        <?php
    }
}

// Add import tools to admin menu
function ln_reader_add_import_tools_menu() {
    add_management_page(
        'LN Reader Import Tools',
        'LN Reader Import',
        'manage_options',
        'ln-reader-import',
        'ln_reader_import_tools_page'
    );
}
add_action('admin_menu', 'ln_reader_add_import_tools_menu');

// Import tools page
function ln_reader_import_tools_page() {
    if (isset($_POST['fix_chapters'])) {
        ln_reader_fix_chapter_relationships();
        echo '<div class="notice notice-success"><p>Chapter relationships fixed!</p></div>';
    }

    if (isset($_POST['map_taxonomies'])) {
        ln_reader_map_imported_taxonomies();
        echo '<div class="notice notice-success"><p>Taxonomies mapped!</p></div>';
    }

    if (isset($_POST['quick_fix_all'])) {
        $result = ln_reader_quick_fix_all_issues();
        echo '<div class="notice notice-success"><p>Quick fix completed! ' . $result['message'] . '</p></div>';
    }

    ?>
    <div class="wrap">
        <h1>LN Reader Import Tools</h1>

        <?php ln_reader_import_status_widget(); ?>

        <h2>Post-Import Tools</h2>
        <p>Use these tools if you need to fix issues after importing:</p>

        <form method="post" style="margin: 20px 0;">
            <input type="submit" name="fix_chapters" class="button button-primary" value="Fix Chapter Relationships" />
            <p class="description">Re-link chapters to their novels based on categories.</p>
        </form>

        <form method="post" style="margin: 20px 0;">
            <input type="submit" name="map_taxonomies" class="button button-primary" value="Map Taxonomies" />
            <p class="description">Map imported taxonomies to theme taxonomies.</p>
        </form>

        <form method="post" style="margin: 20px 0;">
            <input type="submit" name="quick_fix_all" class="button button-primary" value="Quick Fix All Issues" onclick="return confirm('This will fix all chapter numbers and novel relationships. Continue?')" />
            <p class="description">Fix all missing chapter numbers and novel relationships from the error log.</p>
        </form>

        <h3>Import Instructions</h3>
        <ol>
            <li>Go to <strong>Tools → Import</strong></li>
            <li>Install and activate the <strong>WordPress Importer</strong></li>
            <li>Upload your <code>ossantl.WordPress.2025-07-01.xml</code> file</li>
            <li>Map authors as needed</li>
            <li>Check "Download and import file attachments"</li>
            <li>Run the import</li>
            <li>Return here to run post-import tools if needed</li>
        </ol>
    </div>
    <?php
}

// Fix chapter relationships manually
function ln_reader_fix_chapter_relationships() {
    $chapters = get_posts(array(
        'post_type' => 'post',
        'meta_key' => '_chapter_number',
        'posts_per_page' => -1,
        'post_status' => 'any'
    ));

    $fixed_count = 0;

    foreach ($chapters as $chapter) {
        $novel_id = get_post_meta($chapter->ID, '_novel_id', true);

        if (empty($novel_id)) {
            // Try to find novel based on category
            $categories = wp_get_post_categories($chapter->ID);

            foreach ($categories as $cat_id) {
                $category = get_category($cat_id);
                if ($category) {
                    $novels = get_posts(array(
                        'post_type' => 'novel',
                        'title' => $category->name,
                        'posts_per_page' => 1,
                        'post_status' => 'any'
                    ));

                    if (!empty($novels)) {
                        update_post_meta($chapter->ID, '_novel_id', $novels[0]->ID);
                        $fixed_count++;
                        break;
                    }
                }
            }
        }
    }

    error_log("LN Reader: Fixed relationships for $fixed_count chapters");
    return $fixed_count;
}

// Quick fix all issues function
function ln_reader_quick_fix_all_issues() {
    // List of problematic post IDs from error log
    $problematic_posts = array(
        27, 110, 128, 136, 141, 151, 160, 171, 194, 199, 801, 208, 304, 331, 340, 345, 348, 354, 368, 399,
        415, 427, 444, 449, 451, 466, 469, 475, 481, 485, 494, 499, 501, 503, 507, 515, 520, 524, 535, 539,
        549, 562, 566, 572, 575, 584, 590, 592, 596, 598, 602, 604, 606, 608, 610, 612, 616, 618, 620, 624,
        627, 631, 634, 636, 638, 640, 648, 649, 652, 654, 656, 658, 660, 662, 669, 671, 673, 675, 678, 680,
        682, 685, 687, 691, 694, 696, 698, 700, 702, 705, 707, 709, 712, 714, 716, 719, 721, 725, 728, 730,
        733, 735, 738, 740, 742, 745, 747, 749, 758, 760, 762, 764, 767, 774, 776, 778, 780, 783, 785, 787,
        789, 791, 793, 798, 805, 807, 809, 811, 813, 815, 817, 819, 821, 823, 825, 827, 829, 839, 841, 843,
        845, 849, 853, 855, 857, 859, 862, 865, 869, 873, 881, 885, 887, 889, 894, 896, 899, 901, 904, 906,
        908, 931, 937, 940, 944, 947, 950, 953, 955, 957, 960, 962, 970, 973, 975, 981, 983, 986, 988, 990,
        992, 994, 996, 998, 1000, 1003, 1005, 1007, 1009, 1011, 1013, 1015, 1017, 1019, 1021, 1023, 1025,
        1027, 1029, 1031, 1034, 1036, 1038, 1040, 1042, 1044, 1047, 1049, 1051, 1054, 1060, 1063, 1065,
        1067, 1069, 1071, 1073, 1075, 1077, 1088, 1090
    );

    $fixed_count = 0;
    $created_novels = array();

    foreach ($problematic_posts as $post_id) {
        $post = get_post($post_id);
        if (!$post) continue;

        $title = $post->post_title;

        // 1. Fix chapter number
        $chapter_number = get_post_meta($post_id, '_chapter_number', true);
        if (empty($chapter_number)) {
            // Extract from title
            if (preg_match('/ossan chapter (\d+)/i', $title, $matches)) {
                $chapter_number = intval($matches[1]);
                update_post_meta($post_id, '_chapter_number', $chapter_number);
            } elseif (preg_match('/episode (\d+)/i', $title, $matches)) {
                $chapter_number = intval($matches[1]);
                update_post_meta($post_id, '_chapter_number', $chapter_number);
            } elseif (preg_match('/chapter (\d+)/i', $title, $matches)) {
                $chapter_number = intval($matches[1]);
                update_post_meta($post_id, '_chapter_number', $chapter_number);
            } elseif (preg_match('/^(\d+)$/', $title)) {
                $chapter_number = intval($title);
                update_post_meta($post_id, '_chapter_number', $chapter_number);
            }
        }

        // 2. Fix novel relationship
        $novel_id = get_post_meta($post_id, '_novel_id', true);
        if (empty($novel_id)) {
            // Determine novel from title pattern
            if (strpos($title, 'Ossan') !== false) {
                $novel_name = 'Arafoo Kenja no Isekai Seikatsu Nikki';
            } elseif (strpos($title, 'Villainess') !== false) {
                $novel_name = 'Akuyaku Reijou no Oniisan';
            } elseif (strpos($title, 'Flower Pot') !== false) {
                $novel_name = 'Hachi-nan tte, Sore wa Nai deshou!';
            } elseif (strpos($title, 'Episode') !== false) {
                $novel_name = 'Seijo wa Tokkuni Nanika wo Shiteiru you desu ga';
            } else {
                // Try categories
                $categories = wp_get_post_categories($post_id);
                $novel_name = null;
                foreach ($categories as $cat_id) {
                    $category = get_category($cat_id);
                    if ($category && $category->slug !== 'uncategorized') {
                        $novel_name = $category->name;
                        break;
                    }
                }
                if (!$novel_name) $novel_name = 'Unknown Novel';
            }

            // Create/find novel using centralized function
            if (!isset($created_novels[$novel_name])) {
                $novel_id = ln_reader_find_or_create_novel($novel_name);

                if (!$novel_id) {
                    // Create new novel
                    $novel_data = array(
                        'post_title' => $novel_name,
                        'post_type' => 'novel',
                        'post_status' => 'publish',
                        'post_content' => "Novel imported from OssanTL: {$novel_name}",
                    );

                    $novel_id = wp_insert_post($novel_data);

                    if (!is_wp_error($novel_id)) {
                        update_post_meta($novel_id, '_novel_status', 'ongoing');
                        update_post_meta($novel_id, '_author', 'Unknown Author');
                    }
                }

                if ($novel_id && !is_wp_error($novel_id)) {
                    $created_novels[$novel_name] = $novel_id;
                }
            }

            if (isset($created_novels[$novel_name])) {
                update_post_meta($post_id, '_novel_id', $created_novels[$novel_name]);
            }
        }

        $fixed_count++;
    }

    // Update novel statistics
    foreach ($created_novels as $novel_name => $novel_id) {
        $chapters = get_posts(array(
            'post_type' => 'post',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_novel_id',
                    'value' => $novel_id,
                    'compare' => '='
                )
            ),
            'fields' => 'ids'
        ));

        $chapter_count = count($chapters);
        update_post_meta($novel_id, '_chapter_count', $chapter_count);

        if (!empty($chapters)) {
            $latest_chapters = get_posts(array(
                'post_type' => 'post',
                'posts_per_page' => 1,
                'meta_query' => array(
                    array(
                        'key' => '_novel_id',
                        'value' => $novel_id,
                        'compare' => '='
                    )
                ),
                'meta_key' => '_chapter_number',
                'orderby' => 'meta_value_num',
                'order' => 'DESC'
            ));

            if (!empty($latest_chapters)) {
                $latest_chapter = $latest_chapters[0];
                $latest_chapter_number = get_post_meta($latest_chapter->ID, '_chapter_number', true);
                update_post_meta($novel_id, '_latest_chapter', $latest_chapter_number);
                update_post_meta($novel_id, '_latest_chapter_id', $latest_chapter->ID);
            }
        }
    }

    return array(
        'message' => "Fixed {$fixed_count} posts and created/updated " . count($created_novels) . " novels.",
        'fixed_count' => $fixed_count,
        'novels_count' => count($created_novels)
    );
}

// Tambahkan custom image sizes untuk novel thumbnails
function add_novel_image_sizes() {
    add_image_size('novel-featured', 800, 400, true);  // For featured novel
    add_image_size('novel-card', 300, 400, true);      // For novel cards
    add_image_size('novel-thumb', 60, 80, true);       // For popular novels list
}
add_action('after_setup_theme', 'add_novel_image_sizes');

// Tambahkan meta box untuk featured novel
function add_featured_novel_meta_box() {
    add_meta_box(
        'featured_novel_meta',
        'Featured Novel',
        'featured_novel_meta_box_html',
        'novel',
        'side',
        'high'
    );
}
add_action('add_meta_boxes', 'add_featured_novel_meta_box');

// HTML untuk meta box featured novel
function featured_novel_meta_box_html($post) {
    $is_featured = get_post_meta($post->ID, '_is_featured', true);
    ?>
    <label>
        <input type="checkbox" name="is_featured" value="1" <?php checked($is_featured, '1'); ?>>
        Set as Featured Novel
    </label>
    <?php
}

// Save featured novel meta
function save_featured_novel_meta($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (!current_user_can('edit_post', $post_id)) return;
    
    // Update featured status
    $is_featured = isset($_POST['is_featured']) ? '1' : '0';
    update_post_meta($post_id, '_is_featured', $is_featured);
    
    // If this novel is being set as featured, unset any other featured novels
    if ($is_featured === '1') {
        $args = array(
            'post_type' => 'novel',
            'posts_per_page' => -1,
            'meta_key' => '_is_featured',
            'meta_value' => '1',
            'post__not_in' => array($post_id)
        );
        
        $featured_novels = get_posts($args);
        foreach ($featured_novels as $novel) {
            update_post_meta($novel->ID, '_is_featured', '0');
        }
    }
}
add_action('save_post_novel', 'save_featured_novel_meta');

// Fungsi helper untuk mendapatkan last chapter dari novel
function get_novel_last_chapter($novel_id) {
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => 1,
        'meta_key' => '_novel_id',
        'meta_value' => $novel_id,
        'orderby' => 'meta_value_num',
        'order' => 'DESC',
        'meta_query' => array(
            array(
                'key' => '_chapter_number',
                'type' => 'NUMERIC'
            )
        )
    );
    
    $last_chapter = get_posts($args);
    return !empty($last_chapter) ? $last_chapter[0] : null;
}

// Filter novels in archive page
function filter_novel_archive($query) {
    if (!is_admin() && $query->is_main_query() && is_post_type_archive('novel')) {
        // Sort order
        $orderby = isset($_GET['orderby']) ? $_GET['orderby'] : 'date';
        switch ($orderby) {
            case 'title':
                $query->set('orderby', 'title');
                $query->set('order', 'ASC');
                break;
            case 'rating':
                $query->set('meta_key', '_average_rating');
                $query->set('orderby', 'meta_value_num');
                $query->set('order', 'DESC');
                break;
            default: // date
                $query->set('orderby', 'modified');
                $query->set('order', 'DESC');
                break;
        }

        // Time period filter
        $time = isset($_GET['time']) ? $_GET['time'] : 'all';
        if ($time !== 'all') {
            $date_query = array();
            switch ($time) {
                case 'today':
                    $date_query[] = array('after' => '1 day ago');
                    break;
                case 'week':
                    $date_query[] = array('after' => '1 week ago');
                    break;
                case 'month':
                    $date_query[] = array('after' => '1 month ago');
                    break;
            }
            $query->set('date_query', $date_query);
        }

        // Rating filter
        if (isset($_GET['rating']) && !empty($_GET['rating'])) {
            $rating = intval($_GET['rating']);
            $meta_query = array(
                array(
                    'key' => '_average_rating',
                    'value' => $rating,
                    'compare' => '>=',
                    'type' => 'NUMERIC'
                )
            );
            $query->set('meta_query', $meta_query);
        }

        // Set posts per page
        $query->set('posts_per_page', 12);
    }
}
add_action('pre_get_posts', 'filter_novel_archive');

// Add custom feed for latest chapters
function add_latest_chapters_feed() {
    add_feed('latest-chapters', 'generate_latest_chapters_feed');
}
add_action('init', 'add_latest_chapters_feed');

// Generate feed content for latest chapters
function generate_latest_chapters_feed() {
    header('Content-Type: application/rss+xml; charset=UTF-8');

    // Get latest 20 chapters
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => 20,
        'orderby' => 'date',
        'order' => 'DESC',
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'compare' => 'EXISTS'
            )
        )
    );

    $posts = get_posts($args);

    echo '<?xml version="1.0" encoding="UTF-8"?>';
    ?>
    <rss version="2.0"
        xmlns:content="http://purl.org/rss/1.0/modules/content/"
        xmlns:wfw="http://wellformedweb.org/CommentAPI/"
        xmlns:dc="http://purl.org/dc/elements/1.1/"
        xmlns:atom="http://www.w3.org/2005/Atom"
        xmlns:sy="http://purl.org/rss/1.0/modules/syndication/"
        xmlns:slash="http://purl.org/rss/1.0/modules/slash/">
        <channel>
            <title><?php bloginfo_rss('name'); ?> - Latest Chapters</title>
            <atom:link href="<?php self_link(); ?>" rel="self" type="application/rss+xml" />
            <link><?php bloginfo_rss('url'); ?></link>
            <description>Just another translation</description>
            <lastBuildDate><?php echo mysql2date('D, d M Y H:i:s +0000', get_lastpostmodified('GMT'), false); ?></lastBuildDate>
            <language>en-US</language>
            <sy:updatePeriod>
        hourly  </sy:updatePeriod>
            <sy:updateFrequency>
        1       </sy:updateFrequency>
            <generator>https://wordpress.org/?v=<?php echo get_bloginfo('version'); ?></generator>
            <?php
            foreach ($posts as $post) {
                setup_postdata($post);
                $novel_id = get_post_meta($post->ID, '_novel_id', true);
                $novel = get_post($novel_id);
                $chapter_number = get_post_meta($post->ID, '_chapter_number', true);

                if ($novel && $chapter_number) {
                    // Get actual post content for description
                    $post_content = apply_filters('the_content', $post->post_content);
                    $post_content = wp_strip_all_tags($post_content);

                    // Remove extra whitespace and normalize
                    $post_content = trim(preg_replace('/\s+/', ' ', $post_content));

                    // Limit content length for RSS description
                    $excerpt_length = 200;
                    if (strlen($post_content) > $excerpt_length) {
                        $post_content = substr($post_content, 0, $excerpt_length) . '...';
                    }

                    // Use only the actual post content, no added promotional text
                    $description = $post_content;
                    ?>
                    <item>
                        <title><?php echo esc_html($novel->post_title . ' Chapter ' . $chapter_number); ?></title>
                        <link><?php echo esc_url(get_permalink($post->ID)); ?></link>
                        <comments><?php echo esc_url(get_permalink($post->ID)); ?>#respond</comments>
                        <dc:creator><![CDATA[<?php echo get_the_author_meta('display_name', $post->post_author); ?>]]></dc:creator>
                        <pubDate><?php echo mysql2date('D, d M Y H:i:s +0000', $post->post_date_gmt, false); ?></pubDate>
                        <category><![CDATA[<?php echo esc_html($novel->post_title); ?>]]></category>
                        <guid isPermaLink="false"><?php echo home_url('/?p=' . $post->ID); ?></guid>
                        <description><![CDATA[<?php echo $description; ?>]]></description>
                        <wfw:commentRss><?php echo esc_url(get_permalink($post->ID)); ?>feed/</wfw:commentRss>
                        <slash:comments>0</slash:comments>
                    </item>
                    <?php
                }
            }
            wp_reset_postdata();
            ?>
        </channel>
    </rss>
    <?php
}

// Modify the main RSS feed to show novel chapters
function modify_main_feed_query($query) {
    if ($query->is_feed() && $query->is_main_query()) {
        // Only show posts that are chapters (have _novel_id meta)
        $query->set('meta_query', array(
            array(
                'key' => '_novel_id',
                'compare' => 'EXISTS'
            )
        ));
        $query->set('posts_per_page', 20);
    }
}
add_action('pre_get_posts', 'modify_main_feed_query');

// Customize feed title to show novel and chapter
function customize_feed_title($title) {
    if (is_feed()) {
        global $post;
        $novel_id = get_post_meta($post->ID, '_novel_id', true);
        $chapter_number = get_post_meta($post->ID, '_chapter_number', true);

        if ($novel_id && $chapter_number) {
            $novel = get_post($novel_id);
            if ($novel) {
                $title = $novel->post_title . ' Chapter ' . $chapter_number;
            }
        }
    }
    return $title;
}
add_filter('the_title_rss', 'customize_feed_title');

// Customize the main feed content to use actual post content only
function customize_feed_content($content) {
    if (is_feed()) {
        global $post;
        $novel_id = get_post_meta($post->ID, '_novel_id', true);
        $chapter_number = get_post_meta($post->ID, '_chapter_number', true);

        if ($novel_id && $chapter_number) {
            // Get the actual post content
            $post_content = get_the_content();

            // Apply content filters to process shortcodes, etc.
            $post_content = apply_filters('the_content', $post_content);

            // Strip HTML tags and get clean text
            $post_content = wp_strip_all_tags($post_content);

            // Remove extra whitespace and normalize
            $post_content = trim(preg_replace('/\s+/', ' ', $post_content));

            // Limit content length for RSS description (similar to target feed)
            $excerpt_length = 200; // characters - reasonable length for RSS descriptions
            if (strlen($post_content) > $excerpt_length) {
                $post_content = substr($post_content, 0, $excerpt_length) . '...';
            }

            // Return only the actual post content, no added promotional text
            $content = $post_content;
        }
    }
    return $content;
}
add_filter('the_excerpt_rss', 'customize_feed_content');
// Note: Removed the_content_feed filter to eliminate <content:encoded> element
// The reference feed only has <description>, not <content:encoded>

// Remove content:encoded from RSS feed to match reference feed exactly
function remove_content_encoded_from_feed($content) {
    if (is_feed()) {
        return '';
    }
    return $content;
}
add_filter('the_content_feed', 'remove_content_encoded_from_feed', 999);

// Also remove content:encoded via output buffering if needed
function remove_content_encoded_output() {
    if (is_feed()) {
        ob_start(function($buffer) {
            // Remove content:encoded elements completely
            $buffer = preg_replace('/<content:encoded><!\[CDATA\[.*?\]\]><\/content:encoded>\s*/s', '', $buffer);
            return $buffer;
        });
    }
}
add_action('template_redirect', 'remove_content_encoded_output', 1);

// Flush rewrite rules when theme is activated to make the feed work
function flush_feed_rewrite_rules() {
    add_latest_chapters_feed();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'flush_feed_rewrite_rules');

// Force flush rewrite rules for feed
function force_flush_feed_rules() {
    if (isset($_GET['flush_feed'])) {
        flush_rewrite_rules();
        wp_redirect(home_url('/feed/'));
        exit;
    }
}
add_action('init', 'force_flush_feed_rules');

// Set default site description if empty and customize for feed
function set_default_site_description() {
    $description = get_option('blogdescription');
    if (empty($description) || $description === 'Latest light novel translations and updates') {
        update_option('blogdescription', 'Just another translation');
    }
}
add_action('init', 'set_default_site_description');

// Force feed description to match target
function customize_feed_description($output, $show) {
    if ($show === 'description' && is_feed()) {
        return 'Just another translation';
    }
    return $output;
}
add_filter('bloginfo_rss', 'customize_feed_description', 10, 2);

// Customize feed language to match target feed
function customize_feed_language($lang) {
    if (is_feed()) {
        return 'en-US';
    }
    return $lang;
}
add_filter('bloginfo_rss', 'customize_feed_language_filter', 10, 2);

function customize_feed_language_filter($output, $show) {
    if ($show === 'language' && is_feed()) {
        return 'en-US';
    }
    return $output;
}

// Add RSS redirect rules for all case variations
function add_rss_redirect_rules() {
    // Add rewrite rules for RSS variations (case-insensitive)
    add_rewrite_rule('^[Rr][Ss][Ss]/?$', 'index.php?feed=rss2', 'top');
    add_rewrite_rule('^[Rr][Ss][Ss]2?/?$', 'index.php?feed=rss2', 'top');
    add_rewrite_rule('^[Ff][Ee][Ee][Dd]/?$', 'index.php?feed=rss2', 'top');
}
add_action('init', 'add_rss_redirect_rules');

// Handle RSS redirects via template_redirect (more reliable method)
function handle_rss_redirects() {
    // Only run on frontend
    if (is_admin()) {
        return;
    }

    $request_uri = $_SERVER['REQUEST_URI'];
    $path = parse_url($request_uri, PHP_URL_PATH);

    // Get the site path (for subdirectory installations like /epic/)
    $site_url = parse_url(home_url(), PHP_URL_PATH);
    $site_path = trim($site_url, '/');

    // Extract the path after the site directory
    if ($site_path) {
        $pattern = '#^/' . preg_quote($site_path, '#') . '/(.*)$#';
        if (preg_match($pattern, $path, $matches)) {
            $relative_path = $matches[1];
        } else {
            return; // Not in our site directory
        }
    } else {
        $relative_path = trim($path, '/');
    }

    // Check if the path matches RSS variations (case-insensitive)
    if (preg_match('/^(rss|rss2)$/i', $relative_path)) {
        wp_redirect(home_url('/feed/'), 301);
        exit;
    }
}
add_action('template_redirect', 'handle_rss_redirects', 1);

// ============================================================================
// GOOGLE OAUTH INTEGRATION
// ============================================================================

/**
 * Initialize Google OAuth functionality
 */
function ln_reader_init_google_oauth() {
    // Add rewrite rule for OAuth callback
    add_rewrite_rule('^oauth/google/callback/?$', 'index.php?ln_oauth_callback=google', 'top');

    // Debug: Log that the rule was added
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LN Reader: OAuth rewrite rule added');
    }
}
add_action('init', 'ln_reader_init_google_oauth');

/**
 * Flush rewrite rules on theme activation
 */
function ln_reader_flush_oauth_rules() {
    ln_reader_init_google_oauth();
    flush_rewrite_rules(true);

    // Debug: Log that rules were flushed
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LN Reader: Rewrite rules flushed for OAuth');
    }
}
add_action('after_switch_theme', 'ln_reader_flush_oauth_rules');

/**
 * Force flush rewrite rules if OAuth callback is not working
 */
function ln_reader_check_oauth_rules() {
    $rewrite_rules = get_option('rewrite_rules');
    if (!isset($rewrite_rules['^oauth/google/callback/?$'])) {
        ln_reader_init_google_oauth();
        flush_rewrite_rules(true);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LN Reader: OAuth rewrite rule was missing, re-added and flushed');
        }
    }
}
add_action('wp_loaded', 'ln_reader_check_oauth_rules');

/**
 * Add custom query variable for OAuth callback
 */
function ln_reader_add_oauth_query_vars($vars) {
    $vars[] = 'ln_oauth_callback';
    return $vars;
}
add_filter('query_vars', 'ln_reader_add_oauth_query_vars');

/**
 * Handle OAuth callback
 */
function ln_reader_handle_oauth_callback() {
    $oauth_provider = get_query_var('ln_oauth_callback');

    if ($oauth_provider === 'google') {
        ln_reader_handle_google_oauth_callback();
    }
}
add_action('template_redirect', 'ln_reader_handle_oauth_callback');

/**
 * Handle Google OAuth callback
 */
function ln_reader_handle_google_oauth_callback() {
    // Initialize Google OAuth handler
    $google_oauth = new LNReader_GoogleOAuth();

    // Check rate limiting
    $rate_limit_check = $google_oauth->check_rate_limit();
    if (is_wp_error($rate_limit_check)) {
        $google_oauth->log_oauth_activity('rate_limit_exceeded');
        wp_redirect(add_query_arg('login_error', urlencode($rate_limit_check->get_error_message()), home_url('/login')));
        exit;
    }

    // Check for error parameter
    if (isset($_GET['error'])) {
        $error = sanitize_text_field($_GET['error']);
        $error_description = isset($_GET['error_description']) ? sanitize_text_field($_GET['error_description']) : '';

        $google_oauth->log_oauth_activity('oauth_error', array('error' => $error, 'description' => $error_description));
        wp_redirect(add_query_arg('login_error', urlencode('Google login failed: ' . $error_description), home_url('/login')));
        exit;
    }

    // Check for required parameters
    if (!isset($_GET['code']) || !isset($_GET['state'])) {
        $google_oauth->log_oauth_activity('invalid_callback_params');
        wp_redirect(add_query_arg('login_error', urlencode('Invalid OAuth response'), home_url('/login')));
        exit;
    }

    $code = sanitize_text_field($_GET['code']);
    $state = sanitize_text_field($_GET['state']);

    if (!$google_oauth->is_configured()) {
        $google_oauth->log_oauth_activity('not_configured');
        wp_redirect(add_query_arg('login_error', urlencode('Google OAuth is not configured'), home_url('/login')));
        exit;
    }

    // Exchange code for token
    $token_data = $google_oauth->exchange_code_for_token($code, $state);

    if (is_wp_error($token_data)) {
        $google_oauth->log_oauth_activity('token_exchange_failed', array('error' => $token_data->get_error_message()));
        wp_redirect(add_query_arg('login_error', urlencode('Failed to authenticate with Google'), home_url('/login')));
        exit;
    }

    // Get user info
    $user_info = $google_oauth->get_user_info($token_data['access_token']);

    if (is_wp_error($user_info)) {
        $google_oauth->log_oauth_activity('user_info_failed', array('error' => $user_info->get_error_message()));
        wp_redirect(add_query_arg('login_error', urlencode('Failed to get user information from Google'), home_url('/login')));
        exit;
    }

    // Handle login/registration
    $user = $google_oauth->handle_oauth_login($user_info);

    if (is_wp_error($user)) {
        $google_oauth->log_oauth_activity('login_failed', array('error' => $user->get_error_message(), 'email' => $user_info['email'] ?? 'unknown'));
        wp_redirect(add_query_arg('login_error', urlencode($user->get_error_message()), home_url('/login')));
        exit;
    }

    // Success - log the user in
    wp_clear_auth_cookie();
    wp_set_current_user($user->ID);
    wp_set_auth_cookie($user->ID, true);

    // Log successful login
    $google_oauth->log_oauth_activity('login_success', array('user_id' => $user->ID, 'username' => $user->user_login));

    // Redirect based on user role
    if (user_can($user, 'administrator')) {
        wp_redirect(admin_url());
    } else {
        wp_redirect(home_url('/dashboard'));
    }
    exit;
}

/**
 * Get Google OAuth login URL
 */
function ln_reader_get_google_oauth_url() {
    $google_oauth = new LNReader_GoogleOAuth();

    if (!$google_oauth->is_configured()) {
        return false;
    }

    return $google_oauth->get_auth_url();
}

/**
 * Check if Google OAuth is enabled and configured
 */
function ln_reader_is_google_oauth_enabled() {
    $google_oauth = new LNReader_GoogleOAuth();
    return $google_oauth->is_configured();
}

/**
 * Add Google OAuth settings to admin menu
 */
function ln_reader_add_google_oauth_admin_menu() {
    add_submenu_page(
        'options-general.php',
        'Google OAuth Settings',
        'Google OAuth',
        'manage_options',
        'ln-reader-google-oauth',
        'ln_reader_google_oauth_settings_page'
    );
}
add_action('admin_menu', 'ln_reader_add_google_oauth_admin_menu');

/**
 * Google OAuth settings page
 */
function ln_reader_google_oauth_settings_page() {
    // Handle form submission
    if (isset($_POST['submit']) && wp_verify_nonce($_POST['ln_reader_oauth_nonce'], 'ln_reader_oauth_settings')) {
        $client_id = sanitize_text_field($_POST['google_client_id']);
        $client_secret = sanitize_text_field($_POST['google_client_secret']);

        update_option('ln_reader_google_client_id', $client_id);
        update_option('ln_reader_google_client_secret', $client_secret);

        echo '<div class="notice notice-success"><p>Google OAuth settings saved successfully!</p></div>';
    }

    $client_id = get_option('ln_reader_google_client_id', '');
    $client_secret = get_option('ln_reader_google_client_secret', '');
    $redirect_uri = home_url('/oauth/google/callback');
    ?>
    <div class="wrap">
        <h1>Google OAuth Settings</h1>

        <div class="card" style="max-width: 800px;">
            <h2>Setup Instructions</h2>
            <ol>
                <li>Go to the <a href="https://console.developers.google.com/" target="_blank">Google Developers Console</a></li>
                <li>Create a new project or select an existing one</li>
                <li>Enable the Google+ API</li>
                <li>Go to "Credentials" and create an OAuth 2.0 Client ID</li>
                <li>Set the authorized redirect URI to: <code><?php echo esc_html($redirect_uri); ?></code></li>
                <li>Copy the Client ID and Client Secret below</li>
            </ol>
        </div>

        <form method="post" action="">
            <?php wp_nonce_field('ln_reader_oauth_settings', 'ln_reader_oauth_nonce'); ?>

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="google_client_id">Google Client ID</label>
                    </th>
                    <td>
                        <input type="text" id="google_client_id" name="google_client_id"
                               value="<?php echo esc_attr($client_id); ?>" class="regular-text" />
                        <p class="description">Your Google OAuth Client ID</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="google_client_secret">Google Client Secret</label>
                    </th>
                    <td>
                        <input type="password" id="google_client_secret" name="google_client_secret"
                               value="<?php echo esc_attr($client_secret); ?>" class="regular-text" />
                        <p class="description">Your Google OAuth Client Secret</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Redirect URI</th>
                    <td>
                        <code><?php echo esc_html($redirect_uri); ?></code>
                        <p class="description">Use this URL in your Google OAuth configuration</p>
                    </td>
                </tr>
            </table>

            <?php submit_button('Save Settings'); ?>
        </form>

        <div class="card" style="max-width: 800px; margin-top: 20px;">
            <h2>Current Status</h2>
            <?php if (ln_reader_is_google_oauth_enabled()) : ?>
                <p style="color: green;">✓ Google OAuth is properly configured and ready to use.</p>
            <?php else : ?>
                <p style="color: red;">✗ Google OAuth is not configured. Please enter your Client ID and Client Secret above.</p>
            <?php endif; ?>
        </div>
    </div>
    <?php
}
