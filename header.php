<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>
    <?php
    // Debug: Check for any unwanted output
    ob_start();
    wp_body_open();
    $body_open_output = ob_get_clean();
    if (!empty($body_open_output) && trim($body_open_output) !== '') {
        error_log('LN Reader Debug: wp_body_open output: ' . $body_open_output);
    }
    echo $body_open_output;
    ?>
    
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <?php
            if (has_custom_logo()) {
                the_custom_logo();
            } else {
                echo '<a class="navbar-brand" href="' . esc_url(home_url('/')) . '">' . get_bloginfo('name') . '</a>';
            }
            ?>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain" aria-controls="navbarMain" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarMain">
                <?php
                wp_nav_menu(array(
                    'theme_location'  => 'primary',
                    'depth'           => 2,
                    'container'       => false,
                    'menu_class'      => 'navbar-nav ms-auto mb-2 mb-lg-0',
                    'fallback_cb'     => 'WP_Bootstrap_Navwalker::fallback',
                    'walker'          => new WP_Bootstrap_Navwalker()
                ));
                ?>
                
                <!-- User Authentication Menu -->
                <div class="navbar-nav ms-lg-3">
                    <?php if (is_user_logged_in()) :
                        $current_user = wp_get_current_user(); ?>
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user"></i> <?php echo esc_html($current_user->display_name); ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="<?php echo home_url('/dashboard'); ?>">
                                    <i class="fas fa-tachometer-alt"></i> Dashboard
                                </a></li>
                                <?php if (current_user_can('administrator')) : ?>
                                    <li><a class="dropdown-item" href="<?php echo admin_url(); ?>">
                                        <i class="fas fa-cog"></i> Admin Panel
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo wp_logout_url(); ?>">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a></li>
                            </ul>
                        </div>
                    <?php else : ?>
                        <a class="nav-link" href="<?php echo home_url('/login'); ?>">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                        <a class="nav-link" href="<?php echo home_url('/register'); ?>">
                            <i class="fas fa-user-plus"></i> Register
                        </a>
                    <?php endif; ?>
                </div>

                <form class="d-flex ms-lg-3" role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
                    <div class="input-group">
                        <input class="form-control form-control-sm" type="search" placeholder="Search novels..." name="s">
                        <input type="hidden" name="post_type" value="novel">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </nav>

    <!-- Header Ad Placement -->
    <?php if (is_active_sidebar('header-ads')) : ?>
        <div class="header-ads">
            <div class="container">
                <?php dynamic_sidebar('header-ads'); ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container my-4">
