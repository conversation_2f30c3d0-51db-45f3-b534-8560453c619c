<?php
/**
 * Google OAuth Handler for LN Reader Theme
 * 
 * This class handles Google OAuth 2.0 authentication flow
 * using WordPress's built-in HTTP API instead of external libraries
 */

if (!defined('ABSPATH')) {
    exit;
}

class LNReader_GoogleOAuth {
    
    private $client_id;
    private $client_secret;
    private $redirect_uri;
    private $scope;
    
    // Google OAuth 2.0 endpoints
    const GOOGLE_AUTH_URL = 'https://accounts.google.com/o/oauth2/v2/auth';
    const GOOGLE_TOKEN_URL = 'https://oauth2.googleapis.com/token';
    const GOOGLE_USERINFO_URL = 'https://www.googleapis.com/oauth2/v2/userinfo';
    
    public function __construct() {
        $this->client_id = get_option('ln_reader_google_client_id', '');
        $this->client_secret = get_option('ln_reader_google_client_secret', '');
        $this->redirect_uri = home_url('/oauth/google/callback');
        $this->scope = 'openid email profile';
    }
    
    /**
     * Check if Google OAuth is properly configured
     */
    public function is_configured() {
        return !empty($this->client_id) && !empty($this->client_secret);
    }
    
    /**
     * Generate Google OAuth authorization URL
     */
    public function get_auth_url($state = null) {
        if (!$this->is_configured()) {
            return false;
        }

        if (!$state) {
            // Create a more secure state parameter with timestamp
            $state_data = array(
                'nonce' => wp_create_nonce('google_oauth_state'),
                'timestamp' => time(),
                'user_ip' => $this->get_client_ip()
            );
            $state = base64_encode(json_encode($state_data));
        }

        $params = array(
            'client_id' => $this->client_id,
            'redirect_uri' => $this->redirect_uri,
            'scope' => $this->scope,
            'response_type' => 'code',
            'state' => $state,
            'access_type' => 'offline',
            'prompt' => 'consent'
        );

        return self::GOOGLE_AUTH_URL . '?' . http_build_query($params);
    }
    
    /**
     * Exchange authorization code for access token
     */
    public function exchange_code_for_token($code, $state) {
        if (!$this->is_configured()) {
            return new WP_Error('not_configured', 'Google OAuth is not properly configured');
        }

        // Verify state parameter
        try {
            $state_data = json_decode(base64_decode($state), true);

            if (!is_array($state_data) ||
                !isset($state_data['nonce']) ||
                !isset($state_data['timestamp']) ||
                !isset($state_data['user_ip'])) {
                return new WP_Error('invalid_state', 'Invalid state parameter format');
            }

            // Verify nonce
            if (!wp_verify_nonce($state_data['nonce'], 'google_oauth_state')) {
                return new WP_Error('invalid_state', 'Invalid state nonce');
            }

            // Verify timestamp (state valid for 15 minutes)
            if (time() - $state_data['timestamp'] > 900) {
                return new WP_Error('expired_state', 'Authentication request has expired. Please try again.');
            }

            // Verify IP address (optional, can be disabled if causing issues with proxies)
            if ($state_data['user_ip'] !== $this->get_client_ip()) {
                error_log('LN Reader: OAuth IP mismatch. Original: ' . $state_data['user_ip'] . ', Current: ' . $this->get_client_ip());
                // Uncomment to enable strict IP validation
                // return new WP_Error('ip_mismatch', 'IP address mismatch. Please try again.');
            }
        } catch (Exception $e) {
            return new WP_Error('state_parse_error', 'Failed to parse state parameter');
        }
        
        $body = array(
            'client_id' => $this->client_id,
            'client_secret' => $this->client_secret,
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => $this->redirect_uri
        );
        
        $response = wp_remote_post(self::GOOGLE_TOKEN_URL, array(
            'body' => $body,
            'headers' => array(
                'Content-Type' => 'application/x-www-form-urlencoded'
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (isset($data['error'])) {
            return new WP_Error('token_error', $data['error_description'] ?? $data['error']);
        }
        
        return $data;
    }
    
    /**
     * Get user information from Google
     */
    public function get_user_info($access_token) {
        $response = wp_remote_get(self::GOOGLE_USERINFO_URL, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $access_token
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (isset($data['error'])) {
            return new WP_Error('userinfo_error', $data['error']['message'] ?? 'Failed to get user info');
        }
        
        return $data;
    }
    
    /**
     * Handle Google OAuth login/registration
     */
    public function handle_oauth_login($google_user) {
        if (!isset($google_user['email']) || !isset($google_user['id'])) {
            return new WP_Error('invalid_user_data', 'Invalid user data from Google');
        }
        
        $email = sanitize_email($google_user['email']);
        $google_id = sanitize_text_field($google_user['id']);
        $name = sanitize_text_field($google_user['name'] ?? '');
        $first_name = sanitize_text_field($google_user['given_name'] ?? '');
        $last_name = sanitize_text_field($google_user['family_name'] ?? '');
        $picture = esc_url_raw($google_user['picture'] ?? '');
        
        // Check if user already exists by email
        $existing_user = get_user_by('email', $email);
        
        if ($existing_user) {
            // User exists, check if they have Google ID linked
            $stored_google_id = get_user_meta($existing_user->ID, 'google_id', true);
            
            if (empty($stored_google_id)) {
                // Link Google account to existing user
                update_user_meta($existing_user->ID, 'google_id', $google_id);
                update_user_meta($existing_user->ID, 'google_picture', $picture);
            } elseif ($stored_google_id !== $google_id) {
                return new WP_Error('account_conflict', 'This email is associated with a different Google account');
            }
            
            return $existing_user;
        }
        
        // Check if Google ID is already linked to another account
        $users = get_users(array(
            'meta_key' => 'google_id',
            'meta_value' => $google_id,
            'number' => 1
        ));
        
        if (!empty($users)) {
            return $users[0];
        }
        
        // Create new user
        if (!get_option('users_can_register')) {
            return new WP_Error('registration_disabled', 'User registration is currently disabled');
        }
        
        // Generate username from email or name
        $username = $this->generate_username($email, $name);
        
        // Create user
        $user_data = array(
            'user_login' => $username,
            'user_email' => $email,
            'user_pass' => wp_generate_password(20, true, true),
            'display_name' => $name,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'role' => get_option('default_role', 'subscriber')
        );
        
        $user_id = wp_insert_user($user_data);
        
        if (is_wp_error($user_id)) {
            return $user_id;
        }
        
        // Store Google data
        update_user_meta($user_id, 'google_id', $google_id);
        update_user_meta($user_id, 'google_picture', $picture);
        update_user_meta($user_id, 'oauth_provider', 'google');
        
        return get_user_by('id', $user_id);
    }
    
    /**
     * Generate unique username
     */
    private function generate_username($email, $name) {
        // Try email prefix first
        $username = sanitize_user(substr($email, 0, strpos($email, '@')));
        
        // If empty, try name
        if (empty($username) && !empty($name)) {
            $username = sanitize_user(str_replace(' ', '', strtolower($name)));
        }
        
        // Fallback to random
        if (empty($username)) {
            $username = 'user_' . wp_rand(1000, 9999);
        }
        
        // Ensure uniqueness
        $original_username = $username;
        $counter = 1;
        
        while (username_exists($username)) {
            $username = $original_username . '_' . $counter;
            $counter++;
        }
        
        return $username;
    }

    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR');

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);

                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * Rate limiting for OAuth attempts
     */
    public function check_rate_limit($ip = null) {
        if (!$ip) {
            $ip = $this->get_client_ip();
        }

        $transient_key = 'oauth_attempts_' . md5($ip);
        $attempts = get_transient($transient_key);

        if ($attempts === false) {
            $attempts = 0;
        }

        // Allow 5 attempts per hour
        if ($attempts >= 5) {
            return new WP_Error('rate_limit', 'Too many OAuth attempts. Please try again later.');
        }

        // Increment attempts
        set_transient($transient_key, $attempts + 1, HOUR_IN_SECONDS);

        return true;
    }

    /**
     * Log OAuth activity for security monitoring
     */
    public function log_oauth_activity($action, $details = array()) {
        $log_data = array(
            'timestamp' => current_time('mysql'),
            'action' => $action,
            'ip' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'details' => $details
        );

        error_log('LN Reader OAuth Activity: ' . json_encode($log_data));

        // Store in database for admin review (optional)
        // You could create a custom table for OAuth logs if needed
    }
}
