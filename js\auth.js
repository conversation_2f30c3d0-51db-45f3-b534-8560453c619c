/**
 * Authentication and Dashboard JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {

    // Form validation and enhancement
    initFormValidation();

    // Password strength indicator
    initPasswordStrength();

    // Dashboard functionality
    initDashboard();

    // Loading states for forms
    initLoadingStates();

    // Google OAuth functionality
    initGoogleOAuth();
});

/**
 * Initialize form validation
 */
function initFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // Real-time validation for password confirmation
    const passwordFields = document.querySelectorAll('input[name="password"]');
    const confirmFields = document.querySelectorAll('input[name="confirm_password"], input[name="confirm_password"]');
    
    if (passwordFields.length && confirmFields.length) {
        confirmFields.forEach(confirmField => {
            confirmField.addEventListener('input', function() {
                const password = document.querySelector('input[name="password"]').value;
                const confirm = this.value;
                
                if (password !== confirm) {
                    this.setCustomValidity('Passwords do not match');
                    this.classList.add('is-invalid');
                } else {
                    this.setCustomValidity('');
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        });
    }
}

/**
 * Initialize password strength indicator
 */
function initPasswordStrength() {
    const passwordInputs = document.querySelectorAll('input[type="password"][name="password"], input[type="password"][name="new_password"]');
    
    passwordInputs.forEach(input => {
        // Create strength indicator
        const strengthIndicator = document.createElement('div');
        strengthIndicator.className = 'password-strength';
        input.parentNode.appendChild(strengthIndicator);
        
        // Create strength text
        const strengthText = document.createElement('small');
        strengthText.className = 'form-text password-strength-text';
        input.parentNode.appendChild(strengthText);
        
        input.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);
            
            // Update indicator
            strengthIndicator.className = `password-strength ${strength.class}`;
            strengthText.textContent = strength.text;
            strengthText.style.color = strength.color;
        });
    });
}

/**
 * Calculate password strength
 */
function calculatePasswordStrength(password) {
    if (password.length === 0) {
        return { class: '', text: '', color: '' };
    }
    
    let score = 0;
    
    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    
    // Character variety
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    
    if (score < 3) {
        return { class: 'weak', text: 'Weak password', color: '#dc3545' };
    } else if (score < 5) {
        return { class: 'medium', text: 'Medium strength', color: '#ffc107' };
    } else {
        return { class: 'strong', text: 'Strong password', color: '#28a745' };
    }
}

/**
 * Initialize dashboard functionality
 */
function initDashboard() {
    // Tab switching
    const tabLinks = document.querySelectorAll('[data-tab]');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all tabs and contents
            tabLinks.forEach(l => l.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Show corresponding content
            const targetTab = this.getAttribute('data-tab');
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });
    
    // Bookmark removal
    initBookmarkRemoval();
    
    // Reading progress updates
    initReadingProgress();
}

/**
 * Initialize bookmark removal functionality
 */
function initBookmarkRemoval() {
    const removeButtons = document.querySelectorAll('.remove-bookmark');
    
    removeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const novelId = this.getAttribute('data-novel-id');
            const bookmarkItem = this.closest('.border-bottom, .novel-card');
            
            if (confirm('Are you sure you want to remove this bookmark?')) {
                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                this.disabled = true;
                
                // Get nonce and validate
                const nonce = getBookmarkNonce();
                console.log('Auth.js bookmark removal - Novel ID:', novelId, 'Nonce:', nonce);
                console.log('ln_reader_ajax object:', typeof ln_reader_ajax !== 'undefined' ? ln_reader_ajax : 'undefined');

                if (!nonce) {
                    alert('Security token not found. Please refresh the page and try again.');
                    this.innerHTML = '<i class="fas fa-times"></i>';
                    this.disabled = false;
                    return;
                }

                // Make AJAX request
                const ajaxUrl = (typeof ln_reader_ajax !== 'undefined' && ln_reader_ajax.ajax_url)
                    ? ln_reader_ajax.ajax_url
                    : window.location.origin + '/wp-admin/admin-ajax.php';

                console.log('Using AJAX URL:', ajaxUrl);

                fetch(ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'toggle_bookmark',
                        novel_id: novelId,
                        security: nonce
                    })
                })
                .then(response => {
                    console.log('Auth.js bookmark response status:', response.status);
                    // Check if response is ok
                    if (!response.ok) {
                        throw new Error('Network response was not ok: ' + response.status);
                    }
                    return response.text();
                })
                .then(responseText => {
                    console.log('Auth.js bookmark raw response:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('Auth.js JSON parse error:', parseError);
                        throw new Error('Invalid JSON response from server');
                    }

                    // Check if the server returned a success response
                    if (data.success) {
                        // Successful bookmark removal - animate and remove
                        bookmarkItem.style.transition = 'opacity 0.3s, transform 0.3s';
                        bookmarkItem.style.opacity = '0';
                        bookmarkItem.style.transform = 'translateX(-20px)';

                        setTimeout(() => {
                            bookmarkItem.remove();

                            // Check if no more bookmarks
                            const remainingBookmarks = document.querySelectorAll('.remove-bookmark');
                            if (remainingBookmarks.length === 0) {
                                const container = document.querySelector('#bookmarks .card-body');
                                if (container) {
                                    container.innerHTML = '<p class="text-muted mb-0">No bookmarks yet. Start bookmarking your favorite novels!</p>';
                                }
                            }
                        }, 300);
                    } else {
                        // Server returned an error
                        console.error('Server error:', data);
                        alert('Failed to remove bookmark: ' + (data.data || 'Unknown server error'));
                        // Reset button
                        this.innerHTML = '<i class="fas fa-times"></i>';
                        this.disabled = false;
                    }
                })
                .catch(error => {
                    // Network or parsing errors only
                    console.error('Network/Parse error:', error);
                    alert('Failed to remove bookmark. Please check your connection and try again.');
                    // Reset button
                    this.innerHTML = '<i class="fas fa-times"></i>';
                    this.disabled = false;
                });
            }
        });
    });
}

/**
 * Initialize reading progress functionality
 */
function initReadingProgress() {
    // Update reading progress when viewing a chapter
    if (document.body.classList.contains('single-post')) {
        updateReadingProgress();
    }
    
    // Add progress bars to novel cards
    const novelCards = document.querySelectorAll('.novel-card');
    novelCards.forEach(card => {
        addProgressBar(card);
    });
}

/**
 * Update reading progress for current chapter
 */
function updateReadingProgress() {
    const novelId = getNovelIdFromPage();
    const chapterNumber = getChapterNumberFromPage();
    
    if (novelId && chapterNumber) {
        const ajaxUrl = (typeof ln_reader_ajax !== 'undefined' && ln_reader_ajax.ajax_url)
            ? ln_reader_ajax.ajax_url
            : window.location.origin + '/wp-admin/admin-ajax.php';

        fetch(ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'update_reading_progress',
                novel_id: novelId,
                chapter_number: chapterNumber,
                nonce: getReadingProgressNonce()
            })
        })
        .catch(error => {
            console.error('Failed to update reading progress:', error);
        });
    }
}

/**
 * Initialize loading states for forms
 */
function initLoadingStates() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.classList.add('btn-loading');
                submitButton.disabled = true;
                
                // Re-enable after 5 seconds as fallback
                setTimeout(() => {
                    submitButton.classList.remove('btn-loading');
                    submitButton.disabled = false;
                }, 5000);
            }
        });
    });
}

/**
 * Utility functions
 */
function getBookmarkNonce() {
    // Try localized script first (preferred method)
    if (typeof ln_reader_ajax !== 'undefined' && ln_reader_ajax.bookmark_nonce) {
        return ln_reader_ajax.bookmark_nonce;
    }

    // Fallback to meta tag
    const metaNonce = document.querySelector('meta[name="bookmark-nonce"]')?.content;
    if (metaNonce) {
        return metaNonce;
    }

    // Last fallback to general nonce
    if (typeof ln_reader_ajax !== 'undefined' && ln_reader_ajax.nonce) {
        return ln_reader_ajax.nonce;
    }

    console.warn('Bookmark nonce not found. Bookmark functionality may not work.');
    return '';
}

function getReadingProgressNonce() {
    // This should be localized from PHP
    return document.querySelector('meta[name="reading-progress-nonce"]')?.content || '';
}

function getNovelIdFromPage() {
    // Extract novel ID from page data or URL
    return document.querySelector('meta[name="novel-id"]')?.content || null;
}

function getChapterNumberFromPage() {
    // Extract chapter number from page data or URL
    return document.querySelector('meta[name="chapter-number"]')?.content || null;
}

function addProgressBar(novelCard) {
    // Add reading progress bar to novel card if user has progress
    const novelId = novelCard.getAttribute('data-novel-id');
    if (novelId) {
        // This would need to be implemented with server-side data
        // For now, just add the structure
        const progressContainer = document.createElement('div');
        progressContainer.className = 'reading-progress';
        
        const progressBar = document.createElement('div');
        progressBar.className = 'reading-progress-bar';
        progressBar.style.width = '0%'; // This would be calculated server-side
        
        progressContainer.appendChild(progressBar);
        novelCard.appendChild(progressContainer);
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// Add CSS for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

/**
 * Initialize Google OAuth functionality
 */
function initGoogleOAuth() {
    const googleButtons = document.querySelectorAll('.btn-google');

    googleButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add loading state
            this.classList.add('loading');
            this.style.pointerEvents = 'none';

            // Store original text
            const originalText = this.innerHTML;

            // Update button text
            this.innerHTML = '<i class="fab fa-google"></i> Connecting...';

            // If the click fails for any reason, restore the button after 5 seconds
            setTimeout(() => {
                if (this.classList.contains('loading')) {
                    this.classList.remove('loading');
                    this.style.pointerEvents = '';
                    this.innerHTML = originalText;
                }
            }, 5000);
        });
    });

    // Handle OAuth callback errors
    handleOAuthErrors();
}

/**
 * Handle OAuth callback errors
 */
function handleOAuthErrors() {
    const urlParams = new URLSearchParams(window.location.search);
    const loginError = urlParams.get('login_error');

    if (loginError) {
        showNotification(decodeURIComponent(loginError), 'danger');

        // Clean up URL
        const url = new URL(window.location);
        url.searchParams.delete('login_error');
        window.history.replaceState({}, document.title, url.toString());
    }
}

/**
 * Enhanced form validation for OAuth integration
 */
function validateOAuthCompatibility() {
    // Check if the browser supports the required features
    if (!window.fetch) {
        console.warn('OAuth functionality may not work properly in this browser');
        return false;
    }

    if (!window.URLSearchParams) {
        console.warn('URL parameter handling may not work properly in this browser');
        return false;
    }

    return true;
}

// Initialize OAuth compatibility check
if (!validateOAuthCompatibility()) {
    console.warn('Some OAuth features may not be available in this browser');
}
