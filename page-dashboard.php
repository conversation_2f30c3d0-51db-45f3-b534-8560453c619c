<?php
/*
Template Name: Dashboard
*/

// Redirect if not logged in
if (!is_user_logged_in()) {
    wp_redirect(home_url('/login'));
    exit;
}

get_header();

// Add bookmark nonce to head for auth.js
add_action('wp_head', function() {
    echo '<meta name="bookmark-nonce" content="' . wp_create_nonce('bookmark_nonce') . '">';
});

$current_user = wp_get_current_user();
$user_id = get_current_user_id();

// Get session errors
if (!session_id()) {
    session_start();
}
$password_errors = isset($_SESSION['password_change_errors']) ? $_SESSION['password_change_errors'] : array();
$profile_errors = isset($_SESSION['profile_update_errors']) ? $_SESSION['profile_update_errors'] : array();
unset($_SESSION['password_change_errors']);
unset($_SESSION['profile_update_errors']);

// Get user data
$reading_history = ln_reader_get_reading_history($user_id, 5);
$bookmarked_novels = get_user_meta($user_id, 'bookmarked_novels', true);
if (!is_array($bookmarked_novels)) {
    $bookmarked_novels = array();
}
?>

<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="avatar-circle mx-auto mb-2">
                            <?php echo strtoupper(substr($current_user->display_name, 0, 1)); ?>
                        </div>
                        <h5 class="mb-1"><?php echo esc_html($current_user->display_name); ?></h5>
                        <small class="text-muted"><?php echo esc_html($current_user->user_email); ?></small>
                    </div>
                </div>
            </div>
            
            <div class="list-group mt-3">
                <a href="#overview" class="list-group-item list-group-item-action active" data-tab="overview">
                    <i class="fas fa-tachometer-alt"></i> Overview
                </a>
                <a href="#reading-history" class="list-group-item list-group-item-action" data-tab="reading-history">
                    <i class="fas fa-history"></i> Reading History
                </a>
                <a href="#bookmarks" class="list-group-item list-group-item-action" data-tab="bookmarks">
                    <i class="fas fa-bookmark"></i> Bookmarks
                </a>
                <a href="#profile" class="list-group-item list-group-item-action" data-tab="profile">
                    <i class="fas fa-user"></i> Profile Settings
                </a>
                <a href="#password" class="list-group-item list-group-item-action" data-tab="password">
                    <i class="fas fa-key"></i> Change Password
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9">
            <?php
            // Display success messages
            if (isset($_GET['registered'])) {
                echo '<div class="alert alert-success">Welcome! Your account has been created successfully.</div>';
            }
            if (isset($_GET['password_changed'])) {
                echo '<div class="alert alert-success">Password changed successfully.</div>';
            }
            if (isset($_GET['profile_updated'])) {
                echo '<div class="alert alert-success">Profile updated successfully.</div>';
            }
            ?>

            <!-- Overview Tab -->
            <div class="tab-content active" id="overview">
                <h2 class="mb-4">Dashboard Overview</h2>
                
                <div class="row mb-4">
                    <div class="col-md-4 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo count($bookmarked_novels); ?></h4>
                                        <p class="mb-0">Bookmarked Novels</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-bookmark fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo count($reading_history); ?></h4>
                                        <p class="mb-0">Recently Read</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-book-reader fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo date('M j', strtotime($current_user->user_registered)); ?></h4>
                                        <p class="mb-0">Member Since</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Reading Activity</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($reading_history)) : ?>
                            <?php foreach ($reading_history as $item) : ?>
                                <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                    <div class="flex-shrink-0 me-3">
                                        <?php if (has_post_thumbnail($item['novel']->ID)) : ?>
                                            <?php echo get_the_post_thumbnail($item['novel']->ID, 'thumbnail', array('class' => 'rounded', 'style' => 'width: 50px; height: 70px; object-fit: cover;')); ?>
                                        <?php else : ?>
                                            <div class="bg-secondary rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 70px;">
                                                <i class="fas fa-book text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo esc_html($item['novel']->post_title); ?></h6>
                                        <?php
                                        $chapter_number = isset($item['chapter']) && $item['chapter'] ? get_post_meta($item['chapter']->ID, '_chapter_number', true) : '';
                                        ?>
                                        <p class="mb-1 text-muted">Chapter <?php echo esc_html($chapter_number ?: 'Unknown'); ?></p>
                                        <?php
                                        $timestamp = isset($item['timestamp']) ? $item['timestamp'] : 0;
                                        ?>
                                        <small class="text-muted"><?php echo $timestamp ? human_time_diff($timestamp, current_time('timestamp')) . ' ago' : 'Unknown time'; ?></small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <?php if (isset($item['chapter']) && $item['chapter']) : ?>
                                            <a href="<?php echo get_permalink($item['chapter']->ID); ?>" class="btn btn-sm btn-outline-primary">Continue</a>
                                        <?php else : ?>
                                            <span class="btn btn-sm btn-outline-secondary disabled">Chapter not found</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else : ?>
                            <p class="text-muted mb-0">No reading history yet. Start reading some novels!</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Reading History Tab -->
            <div class="tab-content" id="reading-history">
                <h2 class="mb-4">Reading History</h2>
                <div class="card">
                    <div class="card-body">
                        <?php 
                        $full_history = ln_reader_get_reading_history($user_id, 20);
                        if (!empty($full_history)) : ?>
                            <?php foreach ($full_history as $item) : ?>
                                <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                    <div class="flex-shrink-0 me-3">
                                        <?php if (has_post_thumbnail($item['novel']->ID)) : ?>
                                            <?php echo get_the_post_thumbnail($item['novel']->ID, 'thumbnail', array('class' => 'rounded', 'style' => 'width: 60px; height: 80px; object-fit: cover;')); ?>
                                        <?php else : ?>
                                            <div class="bg-secondary rounded d-flex align-items-center justify-content-center" style="width: 60px; height: 80px;">
                                                <i class="fas fa-book text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="<?php echo get_permalink($item['novel']->ID); ?>" class="text-decoration-none">
                                                <?php echo esc_html($item['novel']->post_title); ?>
                                            </a>
                                        </h6>
                                        <?php
                                        $chapter_number = isset($item['chapter']) && $item['chapter'] ? get_post_meta($item['chapter']->ID, '_chapter_number', true) : '';
                                        ?>
                                        <p class="mb-1 text-muted">Last read: Chapter <?php echo esc_html($chapter_number ?: 'Unknown'); ?></p>
                                        <?php
                                        $timestamp = isset($item['timestamp']) ? $item['timestamp'] : 0;
                                        ?>
                                        <small class="text-muted"><?php echo $timestamp ? date('M j, Y', $timestamp) : 'Unknown date'; ?></small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <?php if (isset($item['chapter']) && $item['chapter']) : ?>
                                            <a href="<?php echo get_permalink($item['chapter']->ID); ?>" class="btn btn-sm btn-primary">Continue Reading</a>
                                        <?php else : ?>
                                            <span class="btn btn-sm btn-secondary disabled">Chapter not found</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else : ?>
                            <p class="text-muted mb-0">No reading history yet. Start exploring our novel collection!</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Bookmarks Tab -->
            <div class="tab-content" id="bookmarks">
                <h2 class="mb-4">My Bookmarks</h2>
                <div class="card">
                    <div class="card-body">
                        <?php if (!empty($bookmarked_novels)) : 
                            $novel_query = new WP_Query(array(
                                'post_type' => 'novel',
                                'post__in' => $bookmarked_novels,
                                'posts_per_page' => -1
                            ));
                            
                            if ($novel_query->have_posts()) : 
                                while ($novel_query->have_posts()) : $novel_query->the_post(); ?>
                                    <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                        <div class="flex-shrink-0 me-3">
                                            <?php if (has_post_thumbnail()) : ?>
                                                <?php the_post_thumbnail('thumbnail', array('class' => 'rounded', 'style' => 'width: 60px; height: 80px; object-fit: cover;')); ?>
                                            <?php else : ?>
                                                <div class="bg-secondary rounded d-flex align-items-center justify-content-center" style="width: 60px; height: 80px;">
                                                    <i class="fas fa-book text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <a href="<?php the_permalink(); ?>" class="text-decoration-none">
                                                    <?php the_title(); ?>
                                                </a>
                                            </h6>
                                            <p class="mb-1 text-muted"><?php echo wp_trim_words(get_the_excerpt(), 15); ?></p>
                                            <small class="text-muted">Updated: <?php echo get_the_modified_date(); ?></small>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <a href="<?php the_permalink(); ?>" class="btn btn-sm btn-primary me-2">Read</a>
                                            <button class="btn btn-sm btn-outline-danger remove-bookmark" data-novel-id="<?php echo get_the_ID(); ?>">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                <?php endwhile;
                                wp_reset_postdata();
                            endif;
                        else : ?>
                            <p class="text-muted mb-0">No bookmarks yet. Start bookmarking your favorite novels!</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Profile Settings Tab -->
            <div class="tab-content" id="profile">
                <h2 class="mb-4">Profile Settings</h2>

                <?php if (!empty($profile_errors)) : ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($profile_errors as $error) : ?>
                                <li><?php echo esc_html($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form method="post" action="">
                            <?php wp_nonce_field('ln_reader_update_profile', 'ln_reader_update_profile_nonce'); ?>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="display_name" class="form-label">Display Name *</label>
                                    <input type="text" class="form-control" id="display_name" name="display_name"
                                           value="<?php echo esc_attr($current_user->display_name); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo esc_attr($current_user->user_email); ?>" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="bio" class="form-label">Bio</label>
                                <textarea class="form-control" id="bio" name="bio" rows="4"
                                          placeholder="Tell us about yourself..."><?php echo esc_textarea(get_user_meta($user_id, 'description', true)); ?></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Profile
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Change Password Tab -->
            <div class="tab-content" id="password">
                <h2 class="mb-4">Change Password</h2>

                <?php if (!empty($password_errors)) : ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($password_errors as $error) : ?>
                                <li><?php echo esc_html($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form method="post" action="" id="passwordForm">
                            <?php wp_nonce_field('ln_reader_password_change', 'ln_reader_password_change_nonce'); ?>

                            <div class="mb-3">
                                <label for="current_password" class="form-label">Current Password *</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>

                            <div class="mb-3">
                                <label for="new_password" class="form-label">New Password *</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <div class="form-text">Minimum 6 characters.</div>
                            </div>

                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key"></i> Change Password
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: bold;
}

.list-group-item {
    border: none;
    border-radius: 8px !important;
    margin-bottom: 5px;
}

.list-group-item.active {
    background-color: #007bff;
    border-color: #007bff;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item.active:hover {
    background-color: #007bff;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 10px;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    border-radius: 8px;
}

.alert {
    border-radius: 8px;
    border: none;
}

@media (max-width: 991px) {
    .list-group {
        display: flex;
        flex-direction: row;
        overflow-x: auto;
        margin-bottom: 20px;
    }

    .list-group-item {
        white-space: nowrap;
        margin-right: 10px;
        margin-bottom: 0;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching
    const tabLinks = document.querySelectorAll('[data-tab]');
    const tabContents = document.querySelectorAll('.tab-content');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all tabs and contents
            tabLinks.forEach(l => l.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Show corresponding content
            const targetTab = this.getAttribute('data-tab');
            document.getElementById(targetTab).classList.add('active');
        });
    });

    // Password validation
    const passwordForm = document.getElementById('passwordForm');
    if (passwordForm) {
        const newPassword = document.getElementById('new_password');
        const confirmPassword = document.getElementById('confirm_password');

        function validatePasswords() {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        }

        newPassword.addEventListener('input', validatePasswords);
        confirmPassword.addEventListener('input', validatePasswords);
    }

    // Note: Bookmark removal functionality is handled by auth.js
    // which is loaded on dashboard pages. No duplicate handlers needed here.
});
</script>

<?php get_footer(); ?>
