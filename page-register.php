<?php
/*
Template Name: Register
*/

// Redirect if already logged in
if (is_user_logged_in()) {
    if (current_user_can('administrator')) {
        wp_redirect(admin_url());
    } else {
        wp_redirect(home_url('/dashboard'));
    }
    exit;
}

// Check if registration is enabled
if (!get_option('users_can_register')) {
    wp_redirect(home_url('/login'));
    exit;
}

// Debug: Log page access
error_log('LN Reader: Register page accessed. POST data: ' . (empty($_POST) ? 'None' : 'Present'));

get_header();

// Get registration errors from session
if (!session_id()) {
    session_start();
}
$errors = isset($_SESSION['registration_errors']) ? $_SESSION['registration_errors'] : array();
unset($_SESSION['registration_errors']);
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h2 class="h4 mb-2">Create Account</h2>
                        <p class="text-muted">Join our community of readers</p>
                    </div>

                    <?php if (!empty($errors)) : ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error) : ?>
                                    <li><?php echo esc_html($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="" id="registerForm">
                        <?php wp_nonce_field('ln_reader_register', 'ln_reader_register_nonce'); ?>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?php echo isset($_POST['username']) ? esc_attr($_POST['username']) : ''; ?>" required>
                            <div class="form-text">Only letters, numbers, and underscores allowed.</div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo isset($_POST['email']) ? esc_attr($_POST['email']) : ''; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">Minimum 6 characters.</div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm Password *</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and 
                                <a href="#" class="text-decoration-none">Privacy Policy</a>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus"></i> Create Account
                        </button>
                    </form>

                    <?php if (ln_reader_is_google_oauth_enabled()) : ?>
                        <div class="social-login">
                            <div class="text-center mb-3">
                                <span class="text-muted">or</span>
                            </div>
                            <a href="<?php echo esc_url(ln_reader_get_google_oauth_url()); ?>" class="btn btn-google w-100 mb-3">
                                <i class="fab fa-google"></i> Sign up with Google
                            </a>
                        </div>
                    <?php endif; ?>

                    <div class="text-center">
                        <p class="mb-0">
                            Already have an account?
                            <a href="<?php echo home_url('/login'); ?>" class="text-decoration-none">
                                Sign in here
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.card-body {
    background: #fff;
    border-radius: 10px;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    border-radius: 8px;
    padding: 12px;
    font-weight: 500;
}

.alert {
    border-radius: 8px;
    border: none;
}

.alert ul {
    padding-left: 20px;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.text-muted {
    color: #6c757d !important;
}

@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .col-md-6 {
        padding-left: 0;
        padding-right: 0;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registerForm');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePasswords() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
});
</script>

<?php get_footer(); ?>
