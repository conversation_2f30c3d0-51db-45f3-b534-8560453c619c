<?php
/*
Template Name: Reset Password
*/

// Redirect if already logged in
if (is_user_logged_in()) {
    if (current_user_can('administrator')) {
        wp_redirect(admin_url());
    } else {
        wp_redirect(home_url('/dashboard'));
    }
    exit;
}

get_header();

$action = isset($_GET['action']) ? $_GET['action'] : '';
$key = isset($_GET['key']) ? $_GET['key'] : '';
$login = isset($_GET['login']) ? $_GET['login'] : '';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body p-4">
                    <?php if ($action === 'rp' && $key && $login) : 
                        // Handle password reset form
                        $user = check_password_reset_key($key, $login);
                        if (is_wp_error($user)) : ?>
                            <div class="text-center mb-4">
                                <h2 class="h4 mb-2 text-danger">Invalid Reset Link</h2>
                                <p class="text-muted">This password reset link is invalid or has expired.</p>
                            </div>
                            <div class="text-center">
                                <a href="<?php echo home_url('/reset-password'); ?>" class="btn btn-primary">
                                    Request New Reset Link
                                </a>
                            </div>
                        <?php else : ?>
                            <div class="text-center mb-4">
                                <h2 class="h4 mb-2">Reset Password</h2>
                                <p class="text-muted">Enter your new password below</p>
                            </div>

                            <form method="post" action="<?php echo esc_url(site_url('wp-login.php?action=resetpass')); ?>">
                                <input type="hidden" name="key" value="<?php echo esc_attr($key); ?>">
                                <input type="hidden" name="login" value="<?php echo esc_attr($login); ?>">
                                
                                <div class="mb-3">
                                    <label for="pass1" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="pass1" name="pass1" required>
                                </div>

                                <div class="mb-3">
                                    <label for="pass2" class="form-label">Confirm New Password</label>
                                    <input type="password" class="form-control" id="pass2" name="pass2" required>
                                </div>

                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-key"></i> Reset Password
                                </button>
                            </form>
                        <?php endif; ?>
                    <?php else : ?>
                        <div class="text-center mb-4">
                            <h2 class="h4 mb-2">Forgot Password?</h2>
                            <p class="text-muted">Enter your email to receive a reset link</p>
                        </div>

                        <?php
                        // Display messages
                        if (isset($_GET['reset_error'])) {
                            echo '<div class="alert alert-danger">' . esc_html(urldecode($_GET['reset_error'])) . '</div>';
                        }
                        
                        if (isset($_GET['reset_sent'])) {
                            echo '<div class="alert alert-success">Password reset email sent! Check your inbox.</div>';
                        }
                        ?>

                        <form method="post" action="">
                            <?php wp_nonce_field('ln_reader_reset', 'ln_reader_reset_nonce'); ?>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo isset($_POST['email']) ? esc_attr($_POST['email']) : ''; ?>" required>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="fas fa-envelope"></i> Send Reset Link
                            </button>
                        </form>
                    <?php endif; ?>

                    <div class="text-center">
                        <p class="mb-2">
                            <a href="<?php echo home_url('/login'); ?>" class="text-decoration-none">
                                <i class="fas fa-arrow-left"></i> Back to Login
                            </a>
                        </p>
                        <p class="mb-0">
                            Don't have an account? 
                            <a href="<?php echo home_url('/register'); ?>" class="text-decoration-none">
                                Sign up here
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.card-body {
    background: #fff;
    border-radius: 10px;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    border-radius: 8px;
    padding: 12px;
    font-weight: 500;
}

.alert {
    border-radius: 8px;
    border: none;
}

.text-muted {
    color: #6c757d !important;
}

@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .col-md-6 {
        padding-left: 0;
        padding-right: 0;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const pass1 = document.getElementById('pass1');
    const pass2 = document.getElementById('pass2');
    
    if (pass1 && pass2) {
        function validatePasswords() {
            if (pass1.value !== pass2.value) {
                pass2.setCustomValidity('Passwords do not match');
            } else {
                pass2.setCustomValidity('');
            }
        }
        
        pass1.addEventListener('input', validatePasswords);
        pass2.addEventListener('input', validatePasswords);
    }
});
</script>

<?php get_footer(); ?>
