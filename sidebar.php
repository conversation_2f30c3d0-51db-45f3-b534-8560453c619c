<?php
/**
 * The sidebar containing the main widget area with AdSense-friendly ad placements
 *
 * @package LN_Reader
 */

if (!is_active_sidebar('sidebar-main') && 
    !is_active_sidebar('sidebar-top-ads') && 
    !is_active_sidebar('sidebar-middle-ads') && 
    !is_active_sidebar('sidebar-bottom-ads')) {
    return;
}
?>

<aside id="secondary" class="widget-area sidebar">
    <!-- Sidebar Top Ad Placement -->
    <?php if (is_active_sidebar('sidebar-top-ads')) : ?>
        <div class="sidebar-top-ads">
            <?php dynamic_sidebar('sidebar-top-ads'); ?>
        </div>
    <?php endif; ?>

    <!-- Popular Novels Widget -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Popular Novels</h5>
        </div>
        <div class="card-body">
            <?php
            $popular_novels = new WP_Query(array(
                'post_type' => 'novel',
                'posts_per_page' => 5,
                'meta_key' => '_views_count',
                'orderby' => 'meta_value_num',
                'order' => 'DESC'
            ));

            if ($popular_novels->have_posts()) :
                while ($popular_novels->have_posts()) : $popular_novels->the_post(); ?>
                    <div class="popular-novel-item d-flex mb-3">
                        <div class="novel-thumb me-3">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('thumbnail', array('class' => 'img-fluid')); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="novel-info flex-grow-1">
                            <h6 class="mb-1">
                                <a href="<?php the_permalink(); ?>" class="text-decoration-none">
                                    <?php the_title(); ?>
                                </a>
                            </h6>
                            <small class="text-muted">
                                <?php echo get_post_views(get_the_ID()); ?> views
                            </small>
                        </div>
                    </div>
                <?php endwhile;
                wp_reset_postdata();
            endif;
            ?>
        </div>
    </div>

    <!-- Sidebar Middle Ad Placement -->
    <?php if (is_active_sidebar('sidebar-middle-ads')) : ?>
        <div class="sidebar-middle-ads">
            <?php dynamic_sidebar('sidebar-middle-ads'); ?>
        </div>
    <?php endif; ?>

    <!-- Latest Chapters Widget -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Latest Chapters</h5>
        </div>
        <div class="card-body">
            <?php
            $latest_chapters = new WP_Query(array(
                'post_type' => 'post',
                'posts_per_page' => 8,
                'meta_query' => array(
                    array(
                        'key' => '_novel_id',
                        'compare' => 'EXISTS'
                    )
                )
            ));

            if ($latest_chapters->have_posts()) :
                while ($latest_chapters->have_posts()) : $latest_chapters->the_post();
                    $novel_id = get_post_meta(get_the_ID(), '_novel_id', true);
                    $chapter_number = get_post_meta(get_the_ID(), '_chapter_number', true);
                    $novel = get_post($novel_id);
                    ?>
                    <div class="chapter-item border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="chapter-info flex-grow-1">
                                <?php if ($novel) : ?>
                                    <div class="novel-title small text-muted">
                                        <a href="<?php echo get_permalink($novel); ?>" class="text-decoration-none">
                                            <?php echo $novel->post_title; ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                <div class="chapter-title">
                                    <a href="<?php the_permalink(); ?>" class="text-decoration-none">
                                        Chapter <?php echo $chapter_number; ?>
                                    </a>
                                </div>
                            </div>
                            <div class="chapter-date small text-muted">
                                <?php echo human_time_diff(get_the_time('U'), current_time('timestamp')) . ' ago'; ?>
                            </div>
                        </div>
                    </div>
                <?php endwhile;
                wp_reset_postdata();
            endif;
            ?>
        </div>
    </div>

    <!-- Main Sidebar Widgets -->
    <?php if (is_active_sidebar('sidebar-main')) : ?>
        <?php dynamic_sidebar('sidebar-main'); ?>
    <?php endif; ?>

    <!-- Sidebar Bottom Ad Placement -->
    <?php if (is_active_sidebar('sidebar-bottom-ads')) : ?>
        <div class="sidebar-bottom-ads">
            <?php dynamic_sidebar('sidebar-bottom-ads'); ?>
        </div>
    <?php endif; ?>
</aside><!-- #secondary -->
