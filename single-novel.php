<?php get_header(); ?>

<!-- Breadcrumb Navigation -->
<?php ln_reader_breadcrumbs(); ?>

<?php
wp_enqueue_script('jquery');
wp_localize_script('jquery', 'bookmarkAjax', array(
    'ajaxurl' => admin_url('admin-ajax.php'),
    'security' => wp_create_nonce('bookmark_nonce')
));
?>

<!-- Structured Data for Novel -->
<?php while (have_posts()) : the_post(); ?>
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Book",
    "name": "<?php echo esc_js(get_the_title()); ?>",
    "url": "<?php echo esc_url(get_permalink()); ?>",
    "description": "<?php echo esc_js(wp_trim_words(get_the_excerpt(), 25)); ?>",
    <?php if (has_post_thumbnail()) : ?>
    "image": "<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'large')); ?>",
    <?php endif; ?>
    "author": {
        "@type": "Person",
        "name": "<?php echo esc_js(get_post_meta(get_the_ID(), '_author', true) ?: get_post_meta(get_the_ID(), '_novel_author', true) ?: 'Unknown'); ?>"
    },
    "genre": [
        <?php
        $genres = get_the_terms(get_the_ID(), 'novel_genre');
        if ($genres && !is_wp_error($genres)) {
            $genre_names = array_map(function($genre) { return '"' . esc_js($genre->name) . '"'; }, $genres);
            echo implode(', ', $genre_names);
        } else {
            echo '"Light Novel"';
        }
        ?>
    ],
    "inLanguage": "<?php echo esc_js(get_locale()); ?>",
    "datePublished": "<?php echo esc_js(get_the_date('c')); ?>",
    "dateModified": "<?php echo esc_js(get_the_modified_date('c')); ?>",
    "publisher": {
        "@type": "Organization",
        "name": "<?php echo esc_js(get_bloginfo('name')); ?>",
        "url": "<?php echo esc_url(home_url()); ?>"
    },
    <?php
    $rating = get_novel_rating(get_the_ID());
    $average = isset($rating['average']) ? floatval($rating['average']) : 0;
    $count = isset($rating['count']) ? intval($rating['count']) : 0;
    if ($average > 0 && $count > 0) : ?>
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "<?php echo esc_js($average); ?>",
        "bestRating": "5",
        "worstRating": "1",
        "ratingCount": "<?php echo esc_js($count); ?>"
    },
    <?php endif; ?>
    "workExample": [
        <?php
        $chapters = get_posts([
            'post_type' => 'post',
            'posts_per_page' => 5,
            'meta_key' => '_novel_id',
            'meta_value' => get_the_ID(),
            'orderby' => 'date',
            'order' => 'DESC'
        ]);

        if ($chapters) {
            $chapter_data = array();
            foreach ($chapters as $chapter) {
                $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                $chapter_data[] = '{
                    "@type": "Chapter",
                    "name": "' . esc_js($chapter->post_title) . '",
                    "url": "' . esc_url(get_permalink($chapter->ID)) . '",
                    "position": "' . esc_js($chapter_number) . '"
                }';
            }
            echo implode(', ', $chapter_data);
        }
        ?>
    ]
}
</script>
<?php endwhile; rewind_posts(); ?>

<?php while (have_posts()) : the_post(); ?>
<div class="container novel-detail-container">
    <article class="novel-detail" itemscope itemtype="https://schema.org/Book">
        <div class="row">
            <!-- Novel Cover and Actions -->
            <div class="col-lg-3 col-md-4 mb-4">
                <div class="novel-cover-section">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="novel-cover-container">
                            <?php the_post_thumbnail('novel-large', [
                                'class' => 'novel-cover-main',
                                'alt' => esc_attr(get_the_title()),
                                'loading' => 'eager',
                                'itemprop' => 'image'
                            ]); ?>

                            <!-- Novel Status Badge -->
                            <?php
                            $status = get_post_meta(get_the_ID(), '_novel_status', true);
                            if ($status) : ?>
                                <div class="novel-status-overlay">
                                    <span class="status-badge status-badge--<?php echo esc_attr(strtolower($status)); ?>" aria-label="<?php echo esc_attr(sprintf(__('Status: %s', 'ln-reader'), ucfirst($status))); ?>">
                                        <i class="fas fa-circle" aria-hidden="true"></i>
                                        <?php echo esc_html(ucfirst($status)); ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else : ?>
                        <div class="novel-cover-placeholder">
                            <i class="fas fa-book" aria-hidden="true"></i>
                            <span><?php esc_html_e('No Cover', 'ln-reader'); ?></span>
                        </div>
                    <?php endif; ?>

                    <!-- Rating Section -->
                    <div class="rating-section">
                        <?php
                        $rating = get_novel_rating(get_the_ID());
                        $average = isset($rating['average']) ? floatval($rating['average']) : 0;
                        $count = isset($rating['count']) ? intval($rating['count']) : 0;
                        $has_rated = function_exists('has_user_rated') ? has_user_rated(get_the_ID()) : false;
                        ?>
                        <div class="rating-display" itemprop="aggregateRating" itemscope itemtype="https://schema.org/AggregateRating">
                            <div class="rating-stars" aria-label="<?php echo esc_attr(sprintf(__('Rating: %s out of 5 stars', 'ln-reader'), $average)); ?>">
                                <?php for ($i = 1; $i <= 5; $i++) : ?>
                                    <i class="fa<?php echo ($i <= $average) ? 's' : 'r'; ?> fa-star <?php echo $i <= $average ? 'star-filled' : 'star-empty'; ?>" aria-hidden="true"></i>
                                <?php endfor; ?>
                            </div>
                            <div class="rating-info">
                                <span class="rating-value" itemprop="ratingValue"><?php echo number_format($average, 1); ?></span>
                                <span class="rating-count">
                                    (<span itemprop="ratingCount"><?php echo $count; ?></span> <?php echo _n('rating', 'ratings', $count, 'ln-reader'); ?>)
                                </span>
                                <meta itemprop="bestRating" content="5">
                                <meta itemprop="worstRating" content="1">
                            </div>

                            <?php if (is_user_logged_in() && !$has_rated) : ?>
                                <div class="rate-novel">
                                    <div class="stars-input" role="group" aria-label="<?php esc_attr_e('Rate this novel', 'ln-reader'); ?>">
                                        <?php for ($i = 1; $i <= 5; $i++) : ?>
                                            <button class="star-btn" data-rating="<?php echo $i; ?>" aria-label="<?php echo esc_attr(sprintf(__('Rate %d stars', 'ln-reader'), $i)); ?>">
                                                <i class="far fa-star" aria-hidden="true"></i>
                                            </button>
                                        <?php endfor; ?>
                                    </div>
                                    <small class="rate-instruction"><?php esc_html_e('Click to rate', 'ln-reader'); ?></small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="novel-actions" role="group" aria-label="<?php esc_attr_e('Novel actions', 'ln-reader'); ?>">
                        <?php if (is_user_logged_in()) :
                            $user_id = get_current_user_id();
                            $bookmarked_novels = get_user_meta($user_id, 'bookmarked_novels', true);
                            $is_bookmarked = !empty($bookmarked_novels) && in_array(get_the_ID(), $bookmarked_novels);

                            // Get last read chapter
                            $reading_progress = get_user_meta($user_id, 'reading_progress', true);
                            $last_chapter_id = isset($reading_progress[get_the_ID()]) ? $reading_progress[get_the_ID()] : false;
                            $last_chapter = $last_chapter_id ? get_post($last_chapter_id) : false;
                        ?>
                            <button class="btn-action btn-primary toggle-bookmark"
                                    data-novel-id="<?php echo esc_attr(get_the_ID()); ?>"
                                    data-nonce="<?php echo esc_attr(wp_create_nonce('bookmark_nonce')); ?>"
                                    aria-label="<?php echo $is_bookmarked ? esc_attr__('Remove from bookmarks', 'ln-reader') : esc_attr__('Add to bookmarks', 'ln-reader'); ?>">
                                <i class="<?php echo $is_bookmarked ? 'fas' : 'far'; ?> fa-bookmark" aria-hidden="true"></i>
                                <span class="bookmark-text"><?php echo $is_bookmarked ? esc_html__('Bookmarked', 'ln-reader') : esc_html__('Bookmark', 'ln-reader'); ?></span>
                            </button>

                            <?php if ($last_chapter && $last_chapter->ID) :
                                $chapter_number = intval(get_post_meta($last_chapter->ID, '_chapter_number', true));
                                $continue_url = get_permalink($last_chapter->ID);
                            ?>
                                <a href="<?php echo esc_url($continue_url); ?>" class="btn-action btn-success" aria-label="<?php echo esc_attr(sprintf(__('Continue reading from chapter %d', 'ln-reader'), $chapter_number)); ?>">
                                    <i class="fas fa-play" aria-hidden="true"></i>
                                    <span><?php echo sprintf(esc_html__('Continue Ch. %d', 'ln-reader'), $chapter_number); ?></span>
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php
                        // Get first chapter
                        $first_chapter = get_posts([
                            'post_type' => 'post',
                            'posts_per_page' => 1,
                            'meta_key' => '_novel_id',
                            'meta_value' => get_the_ID(),
                            'meta_query' => [
                                [
                                    'key' => '_chapter_number',
                                    'value' => '1',
                                    'compare' => '='
                                ]
                            ],
                            'orderby' => 'meta_value_num',
                            'order' => 'ASC'
                        ]);

                        if ($first_chapter) : ?>
                            <a href="<?php echo esc_url(get_permalink($first_chapter[0]->ID)); ?>" class="btn-action btn-outline-primary" aria-label="<?php esc_attr_e('Start reading from chapter 1', 'ln-reader'); ?>">
                                <i class="fas fa-play" aria-hidden="true"></i>
                                <span><?php esc_html_e('Start Reading', 'ln-reader'); ?></span>
                            </a>
                        <?php endif; ?>

                        <?php
                        // Get latest chapter
                        $latest_chapter = get_posts([
                            'post_type' => 'post',
                            'posts_per_page' => 1,
                            'meta_key' => '_novel_id',
                            'meta_value' => get_the_ID(),
                            'orderby' => 'date',
                            'order' => 'DESC'
                        ]);

                        if ($latest_chapter) :
                            $latest_chapter_number = get_post_meta($latest_chapter[0]->ID, '_chapter_number', true);
                        ?>
                            <a href="<?php echo esc_url(get_permalink($latest_chapter[0]->ID)); ?>" class="btn-action btn-outline-primary" aria-label="<?php echo esc_attr(sprintf(__('Read latest chapter %s', 'ln-reader'), $latest_chapter_number)); ?>">
                                <i class="fas fa-forward" aria-hidden="true"></i>
                                <span><?php echo sprintf(esc_html__('Latest Ch. %s', 'ln-reader'), $latest_chapter_number); ?></span>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Novel Information -->
            <div class="col-lg-9 col-md-8">
                <div class="novel-main-content">
                    <!-- Novel Header -->
                    <header class="novel-header-info">
                        <h1 class="novel-title" itemprop="name"><?php the_title(); ?></h1>

                        <div class="novel-meta-info">
                            <?php
                            $author = get_post_meta(get_the_ID(), '_author', true) ?: get_post_meta(get_the_ID(), '_novel_author', true);
                            if ($author) : ?>
                                <div class="novel-author" itemprop="author" itemscope itemtype="https://schema.org/Person">
                                    <i class="fas fa-user" aria-hidden="true"></i>
                                    <span itemprop="name"><?php echo esc_html($author); ?></span>
                                </div>
                            <?php endif; ?>

                            <?php
                            $release_year = get_post_meta(get_the_ID(), '_release_year', true);
                            if ($release_year) : ?>
                                <div class="novel-year">
                                    <i class="fas fa-calendar" aria-hidden="true"></i>
                                    <span><?php echo esc_html($release_year); ?></span>
                                </div>
                            <?php endif; ?>

                            <?php
                            $genres = get_the_terms(get_the_ID(), 'novel_genre');
                            if ($genres && !is_wp_error($genres)) : ?>
                                <div class="novel-genres" itemprop="genre">
                                    <i class="fas fa-tags" aria-hidden="true"></i>
                                    <?php foreach ($genres as $index => $genre) : ?>
                                        <a href="<?php echo esc_url(get_term_link($genre)); ?>" class="genre-tag" rel="tag">
                                            <?php echo esc_html($genre->name); ?>
                                        </a><?php if ($index < count($genres) - 1) echo ', '; ?>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </header>

                    <!-- Content Top Ads -->
                    <?php if (is_active_sidebar('content-top-ads')) : ?>
                        <div class="content-top-ads" role="complementary" aria-label="<?php esc_attr_e('Top advertisements', 'ln-reader'); ?>">
                            <?php dynamic_sidebar('content-top-ads'); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Novel Description -->
                    <section class="novel-description">
                        <h2 class="section-title"><?php esc_html_e('Description', 'ln-reader'); ?></h2>
                        <div class="description-content" itemprop="description">
                            <?php
                            $content = get_the_content();
                            if ($content) {
                                echo wp_kses_post(wpautop($content));
                            } else {
                                echo '<p>' . esc_html__('No description available for this novel.', 'ln-reader') . '</p>';
                            }
                            ?>
                        </div>
                    </section>

                    <!-- Novel Statistics -->
                    <section class="novel-statistics">
                        <h2 class="section-title"><?php esc_html_e('Statistics', 'ln-reader'); ?></h2>
                        <div class="stats-grid">
                            <?php
                            $chapter_count = ln_reader_get_chapter_count(get_the_ID());
                            ?>
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-list-ol" aria-hidden="true"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value"><?php echo number_format($chapter_count); ?></div>
                                    <div class="stat-label"><?php echo _n('Chapter', 'Chapters', $chapter_count, 'ln-reader'); ?></div>
                                </div>
                            </div>

                            <?php
                            $views = get_post_meta(get_the_ID(), '_novel_views', true) ?: 0;
                            ?>
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-eye" aria-hidden="true"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value"><?php echo number_format($views); ?></div>
                                    <div class="stat-label"><?php echo _n('View', 'Views', $views, 'ln-reader'); ?></div>
                                </div>
                            </div>

                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-star" aria-hidden="true"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value"><?php echo number_format($average, 1); ?></div>
                                    <div class="stat-label"><?php esc_html_e('Rating', 'ln-reader'); ?></div>
                                </div>
                            </div>

                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-plus" aria-hidden="true"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value"><?php echo get_the_date('M Y'); ?></div>
                                    <div class="stat-label"><?php esc_html_e('Added', 'ln-reader'); ?></div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Content Middle Ads -->
                    <?php if (is_active_sidebar('content-middle-ads')) : ?>
                        <div class="content-middle-ads" role="complementary" aria-label="<?php esc_attr_e('Middle advertisements', 'ln-reader'); ?>">
                            <?php dynamic_sidebar('content-middle-ads'); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Chapter List -->
                    <section class="chapter-list-section">
                        <div class="section-header">
                            <h2 class="section-title"><?php esc_html_e('Chapters', 'ln-reader'); ?></h2>
                            <div class="chapter-controls">
                                <button class="btn btn-sm btn-outline-primary" id="sort-chapters" data-sort="asc" aria-label="<?php esc_attr_e('Sort chapters', 'ln-reader'); ?>">
                                    <i class="fas fa-sort-numeric-down" aria-hidden="true"></i>
                                    <span><?php esc_html_e('Sort', 'ln-reader'); ?></span>
                                </button>
                            </div>
                        </div>

                        <div class="chapter-list" role="list">
                            <?php
                            // Use the enhanced chapter finding function
                            $chapters = ln_reader_get_novel_chapters(get_the_ID());

                            if ($chapters && count($chapters) > 0) :
                                foreach ($chapters as $chapter) :
                                    $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                                    if (empty($chapter_number)) {
                                        // Try to extract chapter number from title
                                        preg_match('/chapter\s*(\d+)/i', $chapter->post_title, $matches);
                                        $chapter_number = isset($matches[1]) ? $matches[1] : '';
                                    }

                                    $chapter_title = get_post_meta($chapter->ID, '_chapter_title', true);
                                    $is_premium = get_post_meta($chapter->ID, '_is_premium', true);
                                    $chapter_url = get_permalink($chapter->ID);
                                    $time_ago = human_time_diff(strtotime($chapter->post_date), current_time('timestamp'));
                            ?>
                                <article class="chapter-item" role="listitem">
                                    <a href="<?php echo esc_url($chapter_url); ?>" class="chapter-link" aria-label="<?php echo esc_attr(sprintf(__('Read Chapter %s: %s', 'ln-reader'), $chapter_number, $chapter->post_title)); ?>">
                                        <div class="chapter-info">
                                            <div class="chapter-number">
                                                <?php if ($chapter_number) : ?>
                                                    <?php esc_html_e('Chapter', 'ln-reader'); ?> <?php echo esc_html($chapter_number); ?>
                                                <?php else : ?>
                                                    <?php echo esc_html($chapter->post_title); ?>
                                                <?php endif; ?>
                                                <?php if ($is_premium) : ?>
                                                    <span class="premium-badge" aria-label="<?php esc_attr_e('Premium chapter', 'ln-reader'); ?>">
                                                        <i class="fas fa-crown" aria-hidden="true"></i>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <?php if ($chapter_title && $chapter_number) : ?>
                                                <div class="chapter-title"><?php echo esc_html($chapter_title); ?></div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="chapter-meta">
                                            <time class="chapter-date" datetime="<?php echo esc_attr(get_the_date('c', $chapter->ID)); ?>">
                                                <?php echo esc_html($time_ago); ?> <?php esc_html_e('ago', 'ln-reader'); ?>
                                            </time>
                                            <i class="fas fa-chevron-right chapter-arrow" aria-hidden="true"></i>
                                        </div>
                                    </a>
                                </article>
                            <?php
                                endforeach;
                            else : ?>
                                <div class="no-chapters-message">
                                    <i class="fas fa-book-open" aria-hidden="true"></i>
                                    <h3><?php esc_html_e('No chapters available', 'ln-reader'); ?></h3>
                                    <p><?php esc_html_e('This novel doesn\'t have any chapters yet. Check back later!', 'ln-reader'); ?></p>
                                    <?php if (current_user_can('administrator')) : ?>
                                        <small class="debug-info">
                                            Debug: Novel ID = <?php echo get_the_ID(); ?><br>
                                            Total posts with _novel_id meta: <?php
                                            $debug_posts = get_posts([
                                                'post_type' => 'post',
                                                'meta_key' => '_novel_id',
                                                'posts_per_page' => -1,
                                                'fields' => 'ids'
                                            ]);
                                            echo count($debug_posts);
                                            ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </article>

    <!-- Content Bottom Ads -->
    <?php if (is_active_sidebar('content-bottom-ads')) : ?>
        <div class="content-bottom-ads" role="complementary" aria-label="<?php esc_attr_e('Bottom advertisements', 'ln-reader'); ?>">
            <?php dynamic_sidebar('content-bottom-ads'); ?>
        </div>
    <?php endif; ?>
</div>
<?php endwhile; ?>




<?php get_footer(); ?>
