<?php get_header(); ?>

<!-- Breadcrumb Navigation -->
<?php ln_reader_breadcrumbs(); ?>

<?php
wp_enqueue_script('jquery');
wp_localize_script('jquery', 'bookmarkAjax', array(
    'ajaxurl' => admin_url('admin-ajax.php'),
    'security' => wp_create_nonce('bookmark_nonce')
));
?>

<!-- Structured Data for Novel -->
<?php while (have_posts()) : the_post(); ?>
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Book",
    "name": "<?php echo esc_js(get_the_title()); ?>",
    "url": "<?php echo esc_url(get_permalink()); ?>",
    "description": "<?php echo esc_js(wp_trim_words(get_the_excerpt(), 25)); ?>",
    <?php if (has_post_thumbnail()) : ?>
    "image": "<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'large')); ?>",
    <?php endif; ?>
    "author": {
        "@type": "Person",
        "name": "<?php echo esc_js(get_post_meta(get_the_ID(), '_author', true) ?: get_post_meta(get_the_ID(), '_novel_author', true) ?: 'Unknown'); ?>"
    },
    "genre": [
        <?php
        $genres = get_the_terms(get_the_ID(), 'novel_genre');
        if ($genres && !is_wp_error($genres)) {
            $genre_names = array_map(function($genre) { return '"' . esc_js($genre->name) . '"'; }, $genres);
            echo implode(', ', $genre_names);
        } else {
            echo '"Light Novel"';
        }
        ?>
    ],
    "inLanguage": "<?php echo esc_js(get_locale()); ?>",
    "datePublished": "<?php echo esc_js(get_the_date('c')); ?>",
    "dateModified": "<?php echo esc_js(get_the_modified_date('c')); ?>",
    "publisher": {
        "@type": "Organization",
        "name": "<?php echo esc_js(get_bloginfo('name')); ?>",
        "url": "<?php echo esc_url(home_url()); ?>"
    },
    <?php
    $rating = get_novel_rating(get_the_ID());
    $average = isset($rating['average']) ? floatval($rating['average']) : 0;
    $count = isset($rating['count']) ? intval($rating['count']) : 0;
    if ($average > 0 && $count > 0) : ?>
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "<?php echo esc_js($average); ?>",
        "bestRating": "5",
        "worstRating": "1",
        "ratingCount": "<?php echo esc_js($count); ?>"
    },
    <?php endif; ?>
    "workExample": [
        <?php
        $chapters = get_posts([
            'post_type' => 'post',
            'posts_per_page' => 5,
            'meta_key' => '_novel_id',
            'meta_value' => get_the_ID(),
            'orderby' => 'date',
            'order' => 'DESC'
        ]);

        if ($chapters) {
            $chapter_data = array();
            foreach ($chapters as $chapter) {
                $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                $chapter_data[] = '{
                    "@type": "Chapter",
                    "name": "' . esc_js($chapter->post_title) . '",
                    "url": "' . esc_url(get_permalink($chapter->ID)) . '",
                    "position": "' . esc_js($chapter_number) . '"
                }';
            }
            echo implode(', ', $chapter_data);
        }
        ?>
    ]
}
</script>
<?php endwhile; rewind_posts(); ?>

<?php while (have_posts()) : the_post(); ?>
<div class="container novel-detail-container">
    <article class="novel-detail" itemscope itemtype="https://schema.org/Book">
        <div class="row">
            <!-- Novel Cover and Actions -->
            <div class="col-lg-3 col-md-4 mb-4">
                <div class="novel-cover-section">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="novel-cover-container">
                            <?php the_post_thumbnail('novel-large', [
                                'class' => 'novel-cover-main',
                                'alt' => esc_attr(get_the_title()),
                                'loading' => 'eager',
                                'itemprop' => 'image'
                            ]); ?>

                            <!-- Novel Status Badge -->
                            <?php
                            $status = get_post_meta(get_the_ID(), '_novel_status', true);
                            if ($status) : ?>
                                <div class="novel-status-overlay">
                                    <span class="status-badge status-badge--<?php echo esc_attr(strtolower($status)); ?>" aria-label="<?php echo esc_attr(sprintf(__('Status: %s', 'ln-reader'), ucfirst($status))); ?>">
                                        <i class="fas fa-circle" aria-hidden="true"></i>
                                        <?php echo esc_html(ucfirst($status)); ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else : ?>
                        <div class="novel-cover-placeholder">
                            <i class="fas fa-book" aria-hidden="true"></i>
                            <span><?php esc_html_e('No Cover', 'ln-reader'); ?></span>
                        </div>
                    <?php endif; ?>

                    <!-- Rating Section -->
                    <div class="rating-section">
                        <?php
                        $rating = get_novel_rating(get_the_ID());
                        $average = isset($rating['average']) ? floatval($rating['average']) : 0;
                        $count = isset($rating['count']) ? intval($rating['count']) : 0;
                        $has_rated = function_exists('has_user_rated') ? has_user_rated(get_the_ID()) : false;
                        ?>
                        <div class="rating-display" itemprop="aggregateRating" itemscope itemtype="https://schema.org/AggregateRating">
                            <div class="rating-stars" aria-label="<?php echo esc_attr(sprintf(__('Rating: %s out of 5 stars', 'ln-reader'), $average)); ?>">
                                <?php for ($i = 1; $i <= 5; $i++) : ?>
                                    <i class="fa<?php echo ($i <= $average) ? 's' : 'r'; ?> fa-star <?php echo $i <= $average ? 'star-filled' : 'star-empty'; ?>" aria-hidden="true"></i>
                                <?php endfor; ?>
                            </div>
                            <div class="rating-info">
                                <span class="rating-value" itemprop="ratingValue"><?php echo number_format($average, 1); ?></span>
                                <span class="rating-count">
                                    (<span itemprop="ratingCount"><?php echo $count; ?></span> <?php echo _n('rating', 'ratings', $count, 'ln-reader'); ?>)
                                </span>
                                <meta itemprop="bestRating" content="5">
                                <meta itemprop="worstRating" content="1">
                            </div>

                            <?php if (is_user_logged_in() && !$has_rated) : ?>
                                <div class="rate-novel">
                                    <div class="stars-input" role="group" aria-label="<?php esc_attr_e('Rate this novel', 'ln-reader'); ?>">
                                        <?php for ($i = 1; $i <= 5; $i++) : ?>
                                            <button class="star-btn" data-rating="<?php echo $i; ?>" aria-label="<?php echo esc_attr(sprintf(__('Rate %d stars', 'ln-reader'), $i)); ?>">
                                                <i class="far fa-star" aria-hidden="true"></i>
                                            </button>
                                        <?php endfor; ?>
                                    </div>
                                    <small class="rate-instruction"><?php esc_html_e('Click to rate', 'ln-reader'); ?></small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="novel-actions" role="group" aria-label="<?php esc_attr_e('Novel actions', 'ln-reader'); ?>">
                        <?php if (is_user_logged_in()) :
                            $user_id = get_current_user_id();
                            $bookmarked_novels = get_user_meta($user_id, 'bookmarked_novels', true);
                            $is_bookmarked = !empty($bookmarked_novels) && in_array(get_the_ID(), $bookmarked_novels);

                            // Get last read chapter
                            $reading_progress = get_user_meta($user_id, 'reading_progress', true);
                            $last_chapter_id = isset($reading_progress[get_the_ID()]) ? $reading_progress[get_the_ID()] : false;
                            $last_chapter = $last_chapter_id ? get_post($last_chapter_id) : false;
                        ?>
                            <button class="btn-action btn-primary toggle-bookmark"
                                    data-novel-id="<?php echo esc_attr(get_the_ID()); ?>"
                                    data-nonce="<?php echo esc_attr(wp_create_nonce('bookmark_nonce')); ?>"
                                    aria-label="<?php echo $is_bookmarked ? esc_attr__('Remove from bookmarks', 'ln-reader') : esc_attr__('Add to bookmarks', 'ln-reader'); ?>">
                                <i class="<?php echo $is_bookmarked ? 'fas' : 'far'; ?> fa-bookmark" aria-hidden="true"></i>
                                <span class="bookmark-text"><?php echo $is_bookmarked ? esc_html__('Bookmarked', 'ln-reader') : esc_html__('Bookmark', 'ln-reader'); ?></span>
                            </button>

                            <?php if ($last_chapter && $last_chapter->ID) :
                                $chapter_number = intval(get_post_meta($last_chapter->ID, '_chapter_number', true));
                                $continue_url = get_permalink($last_chapter->ID);
                            ?>
                                <a href="<?php echo esc_url($continue_url); ?>" class="btn-action btn-success" aria-label="<?php echo esc_attr(sprintf(__('Continue reading from chapter %d', 'ln-reader'), $chapter_number)); ?>">
                                    <i class="fas fa-play" aria-hidden="true"></i>
                                    <span><?php echo sprintf(esc_html__('Continue Ch. %d', 'ln-reader'), $chapter_number); ?></span>
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php
                        // Get first chapter
                        $first_chapter = get_posts([
                            'post_type' => 'post',
                            'posts_per_page' => 1,
                            'meta_key' => '_novel_id',
                            'meta_value' => get_the_ID(),
                            'meta_query' => [
                                [
                                    'key' => '_chapter_number',
                                    'value' => '1',
                                    'compare' => '='
                                ]
                            ],
                            'orderby' => 'meta_value_num',
                            'order' => 'ASC'
                        ]);

                        if ($first_chapter) : ?>
                            <a href="<?php echo esc_url(get_permalink($first_chapter[0]->ID)); ?>" class="btn-action btn-outline-primary" aria-label="<?php esc_attr_e('Start reading from chapter 1', 'ln-reader'); ?>">
                                <i class="fas fa-play" aria-hidden="true"></i>
                                <span><?php esc_html_e('Start Reading', 'ln-reader'); ?></span>
                            </a>
                        <?php endif; ?>

                        <?php
                        // Get latest chapter
                        $latest_chapter = get_posts([
                            'post_type' => 'post',
                            'posts_per_page' => 1,
                            'meta_key' => '_novel_id',
                            'meta_value' => get_the_ID(),
                            'orderby' => 'date',
                            'order' => 'DESC'
                        ]);

                        if ($latest_chapter) :
                            $latest_chapter_number = get_post_meta($latest_chapter[0]->ID, '_chapter_number', true);
                        ?>
                            <a href="<?php echo esc_url(get_permalink($latest_chapter[0]->ID)); ?>" class="btn-action btn-outline-primary" aria-label="<?php echo esc_attr(sprintf(__('Read latest chapter %s', 'ln-reader'), $latest_chapter_number)); ?>">
                                <i class="fas fa-forward" aria-hidden="true"></i>
                                <span><?php echo sprintf(esc_html__('Latest Ch. %s', 'ln-reader'), $latest_chapter_number); ?></span>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Novel Information -->
            <div class="col-lg-9 col-md-8">
                <div class="novel-main-content">
                    <!-- Novel Header -->
                    <header class="novel-header-info">
                        <h1 class="novel-title" itemprop="name"><?php the_title(); ?></h1>

                        <div class="novel-meta-info">
                            <?php
                            $author = get_post_meta(get_the_ID(), '_author', true) ?: get_post_meta(get_the_ID(), '_novel_author', true);
                            if ($author) : ?>
                                <div class="novel-author" itemprop="author" itemscope itemtype="https://schema.org/Person">
                                    <i class="fas fa-user" aria-hidden="true"></i>
                                    <span itemprop="name"><?php echo esc_html($author); ?></span>
                                </div>
                            <?php endif; ?>

                            <?php
                            $release_year = get_post_meta(get_the_ID(), '_release_year', true);
                            if ($release_year) : ?>
                                <div class="novel-year">
                                    <i class="fas fa-calendar" aria-hidden="true"></i>
                                    <span><?php echo esc_html($release_year); ?></span>
                                </div>
                            <?php endif; ?>

                            <?php
                            $genres = get_the_terms(get_the_ID(), 'novel_genre');
                            if ($genres && !is_wp_error($genres)) : ?>
                                <div class="novel-genres" itemprop="genre">
                                    <i class="fas fa-tags" aria-hidden="true"></i>
                                    <?php foreach ($genres as $index => $genre) : ?>
                                        <a href="<?php echo esc_url(get_term_link($genre)); ?>" class="genre-tag" rel="tag">
                                            <?php echo esc_html($genre->name); ?>
                                        </a><?php if ($index < count($genres) - 1) echo ', '; ?>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </header>

                    <!-- Content Top Ads -->
                    <?php if (is_active_sidebar('content-top-ads')) : ?>
                        <div class="content-top-ads" role="complementary" aria-label="<?php esc_attr_e('Top advertisements', 'ln-reader'); ?>">
                            <?php dynamic_sidebar('content-top-ads'); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Novel Description -->
                    <section class="novel-description">
                        <h2 class="section-title"><?php esc_html_e('Description', 'ln-reader'); ?></h2>
                        <div class="description-content" itemprop="description">
                            <?php
                            $content = get_the_content();
                            if ($content) {
                                echo wp_kses_post(wpautop($content));
                            } else {
                                echo '<p>' . esc_html__('No description available for this novel.', 'ln-reader') . '</p>';
                            }
                            ?>
                        </div>
                    </section>

                    <!-- Novel Statistics -->
                    <section class="novel-statistics">
                        <h2 class="section-title"><?php esc_html_e('Statistics', 'ln-reader'); ?></h2>
                        <div class="stats-grid">
                            <?php
                            $total_chapters = get_posts([
                                'post_type' => 'post',
                                'meta_key' => '_novel_id',
                                'meta_value' => get_the_ID(),
                                'posts_per_page' => -1,
                                'fields' => 'ids'
                            ]);
                            $chapter_count = count($total_chapters);
                            ?>
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-list-ol" aria-hidden="true"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value"><?php echo number_format($chapter_count); ?></div>
                                    <div class="stat-label"><?php echo _n('Chapter', 'Chapters', $chapter_count, 'ln-reader'); ?></div>
                                </div>
                            </div>

                            <?php
                            $views = get_post_meta(get_the_ID(), '_novel_views', true) ?: 0;
                            ?>
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-eye" aria-hidden="true"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value"><?php echo number_format($views); ?></div>
                                    <div class="stat-label"><?php echo _n('View', 'Views', $views, 'ln-reader'); ?></div>
                                </div>
                            </div>

                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-star" aria-hidden="true"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value"><?php echo number_format($average, 1); ?></div>
                                    <div class="stat-label"><?php esc_html_e('Rating', 'ln-reader'); ?></div>
                                </div>
                            </div>

                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-plus" aria-hidden="true"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value"><?php echo get_the_date('M Y'); ?></div>
                                    <div class="stat-label"><?php esc_html_e('Added', 'ln-reader'); ?></div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Content Middle Ads -->
                    <?php if (is_active_sidebar('content-middle-ads')) : ?>
                        <div class="content-middle-ads" role="complementary" aria-label="<?php esc_attr_e('Middle advertisements', 'ln-reader'); ?>">
                            <?php dynamic_sidebar('content-middle-ads'); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Chapter List -->
                    <section class="chapter-list-section">
                        <div class="section-header">
                            <h2 class="section-title"><?php esc_html_e('Chapters', 'ln-reader'); ?></h2>
                            <div class="chapter-controls">
                                <button class="btn btn-sm btn-outline-primary" id="sort-chapters" data-sort="asc" aria-label="<?php esc_attr_e('Sort chapters', 'ln-reader'); ?>">
                                    <i class="fas fa-sort-numeric-down" aria-hidden="true"></i>
                                    <span><?php esc_html_e('Sort', 'ln-reader'); ?></span>
                                </button>
                            </div>
                        </div>

                        <div class="chapter-list" role="list">
                            <?php
                            $chapters = get_posts([
                                'post_type' => 'post',
                                'meta_key' => '_novel_id',
                                'meta_value' => get_the_ID(),
                                'posts_per_page' => -1,
                                'orderby' => 'meta_value_num',
                                'meta_key' => '_chapter_number',
                                'order' => 'ASC'
                            ]);

                            if ($chapters) :
                                foreach ($chapters as $chapter) :
                                    $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                                    $chapter_title = get_post_meta($chapter->ID, '_chapter_title', true);
                                    $is_premium = get_post_meta($chapter->ID, '_is_premium', true);
                                    $chapter_url = get_permalink($chapter->ID);
                                    $time_ago = human_time_diff(strtotime($chapter->post_date), current_time('timestamp'));
                            ?>
                                <article class="chapter-item" role="listitem">
                                    <a href="<?php echo esc_url($chapter_url); ?>" class="chapter-link" aria-label="<?php echo esc_attr(sprintf(__('Read Chapter %s: %s', 'ln-reader'), $chapter_number, $chapter->post_title)); ?>">
                                        <div class="chapter-info">
                                            <div class="chapter-number">
                                                <?php esc_html_e('Chapter', 'ln-reader'); ?> <?php echo esc_html($chapter_number); ?>
                                                <?php if ($is_premium) : ?>
                                                    <span class="premium-badge" aria-label="<?php esc_attr_e('Premium chapter', 'ln-reader'); ?>">
                                                        <i class="fas fa-crown" aria-hidden="true"></i>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="chapter-title"><?php echo esc_html($chapter->post_title); ?></div>
                                        </div>
                                        <div class="chapter-meta">
                                            <time class="chapter-date" datetime="<?php echo esc_attr(get_the_date('c', $chapter->ID)); ?>">
                                                <?php echo esc_html($time_ago); ?> <?php esc_html_e('ago', 'ln-reader'); ?>
                                            </time>
                                            <i class="fas fa-chevron-right chapter-arrow" aria-hidden="true"></i>
                                        </div>
                                    </a>
                                </article>
                            <?php
                                endforeach;
                            else : ?>
                                <div class="no-chapters-message">
                                    <i class="fas fa-book-open" aria-hidden="true"></i>
                                    <h3><?php esc_html_e('No chapters available', 'ln-reader'); ?></h3>
                                    <p><?php esc_html_e('This novel doesn\'t have any chapters yet. Check back later!', 'ln-reader'); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </article>

    <!-- Content Bottom Ads -->
    <?php if (is_active_sidebar('content-bottom-ads')) : ?>
        <div class="content-bottom-ads" role="complementary" aria-label="<?php esc_attr_e('Bottom advertisements', 'ln-reader'); ?>">
            <?php dynamic_sidebar('content-bottom-ads'); ?>
        </div>
    <?php endif; ?>
</div>
<?php endwhile; ?>

                    <!-- Read Info -->
                    <div class="read-info">
                        <p class="text-muted small mb-0">
                            Read <?php the_title(); ?> online for free at <?php echo get_bloginfo('name'); ?>. 
                            Updates are released as soon as they're available.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Chapters -->
            <div class="card chapters-section">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h2 class="h5 mb-0"><i class="bi bi-list-ul"></i> Chapters</h2>
                    <div class="chapter-controls">
                        <select class="form-select form-select-sm d-inline-block w-auto" id="chapter-sort">
                            <option value="newest" <?php echo (isset($_GET['sort']) && $_GET['sort'] === 'newest') ? 'selected' : ''; ?>>Newest First</option>
                            <option value="oldest" <?php echo (!isset($_GET['sort']) || $_GET['sort'] === 'oldest') ? 'selected' : ''; ?>>Oldest First</option>
                        </select>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php
                    // Pagination settings
                    $chapters_per_page = 15;
                    $current_page = isset($_GET['chapter_page']) ? max(1, intval($_GET['chapter_page'])) : 1;
                    $offset = ($current_page - 1) * $chapters_per_page;

                    // Get sorting order
                    $sort_order = isset($_GET['sort']) && $_GET['sort'] === 'newest' ? 'DESC' : 'ASC';

                    // First, get total count of chapters for pagination
                    $total_chapters = get_posts(array(
                        'post_type' => 'post',
                        'posts_per_page' => -1,
                        'fields' => 'ids',
                        'meta_query' => array(
                            array(
                                'key' => '_novel_id',
                                'value' => get_the_ID(),
                                'compare' => '='
                            )
                        )
                    ));
                    $total_chapters_count = count($total_chapters);
                    $total_pages = ceil($total_chapters_count / $chapters_per_page);

                    // Get chapters for current page
                    $chapters = get_posts(array(
                        'post_type' => 'post',
                        'posts_per_page' => $chapters_per_page,
                        'offset' => $offset,
                        'orderby' => 'meta_value_num',
                        'meta_key' => '_chapter_number',
                        'order' => $sort_order,
                        'meta_query' => array(
                            array(
                                'key' => '_novel_id',
                                'value' => get_the_ID(),
                                'compare' => '='
                            )
                        )
                    ));

                    if ($chapters) : ?>
                        <div class="chapter-list">
                            <?php foreach ($chapters as $chapter) :
                                $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                                $volume_number = get_post_meta($chapter->ID, '_volume_number', true);
                                
                                $display_title = '';
                                if ($volume_number) {
                                    // Check if volume already contains "Volume" text
                                    if (stripos($volume_number, 'volume') !== false) {
                                        $display_title .= esc_html($volume_number) . ' ';
                                    } else {
                                        $display_title .= 'Volume ' . esc_html($volume_number) . ' ';
                                    }
                                }
                                $display_title .= 'Chapter ' . esc_html($chapter_number);
                            ?>
                                <div class="chapter-item">
                                    <a href="<?php echo esc_url(get_permalink($chapter->ID)); ?>" class="chapter-link">
                                        <span class="chapter-number"><?php echo $display_title; ?></span>
                                        <span class="chapter-date"><?php echo get_the_date('M j, Y', $chapter->ID); ?></span>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1) : ?>
                            <div class="chapter-pagination p-3 border-top">
                                <nav aria-label="Chapter pagination">
                                    <ul class="pagination pagination-sm justify-content-center mb-0">
                                        <!-- Previous Page -->
                                        <?php if ($current_page > 1) :
                                            $prev_url = add_query_arg(array(
                                                'chapter_page' => $current_page - 1,
                                                'sort' => isset($_GET['sort']) ? $_GET['sort'] : 'oldest'
                                            ));
                                        ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?php echo esc_url($prev_url); ?>" aria-label="Previous">
                                                    <span aria-hidden="true">&laquo;</span>
                                                </a>
                                            </li>
                                        <?php else : ?>
                                            <li class="page-item disabled">
                                                <span class="page-link" aria-label="Previous">
                                                    <span aria-hidden="true">&laquo;</span>
                                                </span>
                                            </li>
                                        <?php endif; ?>

                                        <!-- Page Numbers -->
                                        <?php
                                        $start_page = max(1, $current_page - 2);
                                        $end_page = min($total_pages, $current_page + 2);

                                        // Show first page if we're not starting from it
                                        if ($start_page > 1) :
                                            $first_url = add_query_arg(array(
                                                'chapter_page' => 1,
                                                'sort' => isset($_GET['sort']) ? $_GET['sort'] : 'oldest'
                                            ));
                                        ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?php echo esc_url($first_url); ?>">1</a>
                                            </li>
                                            <?php if ($start_page > 2) : ?>
                                                <li class="page-item disabled">
                                                    <span class="page-link">...</span>
                                                </li>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <!-- Current range of pages -->
                                        <?php for ($i = $start_page; $i <= $end_page; $i++) :
                                            $page_url = add_query_arg(array(
                                                'chapter_page' => $i,
                                                'sort' => isset($_GET['sort']) ? $_GET['sort'] : 'oldest'
                                            ));
                                        ?>
                                            <li class="page-item <?php echo ($i === $current_page) ? 'active' : ''; ?>">
                                                <a class="page-link" href="<?php echo esc_url($page_url); ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>

                                        <!-- Show last page if we're not ending with it -->
                                        <?php if ($end_page < $total_pages) : ?>
                                            <?php if ($end_page < $total_pages - 1) : ?>
                                                <li class="page-item disabled">
                                                    <span class="page-link">...</span>
                                                </li>
                                            <?php endif; ?>
                                            <?php
                                            $last_url = add_query_arg(array(
                                                'chapter_page' => $total_pages,
                                                'sort' => isset($_GET['sort']) ? $_GET['sort'] : 'oldest'
                                            ));
                                            ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?php echo esc_url($last_url); ?>"><?php echo $total_pages; ?></a>
                                            </li>
                                        <?php endif; ?>

                                        <!-- Next Page -->
                                        <?php if ($current_page < $total_pages) :
                                            $next_url = add_query_arg(array(
                                                'chapter_page' => $current_page + 1,
                                                'sort' => isset($_GET['sort']) ? $_GET['sort'] : 'oldest'
                                            ));
                                        ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?php echo esc_url($next_url); ?>" aria-label="Next">
                                                    <span aria-hidden="true">&raquo;</span>
                                                </a>
                                            </li>
                                        <?php else : ?>
                                            <li class="page-item disabled">
                                                <span class="page-link" aria-label="Next">
                                                    <span aria-hidden="true">&raquo;</span>
                                                </span>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>

                                <!-- Pagination Info -->
                                <div class="pagination-info text-center mt-2">
                                    <small class="text-muted">
                                        Showing <?php echo (($current_page - 1) * $chapters_per_page + 1); ?>-<?php echo min($current_page * $chapters_per_page, $total_chapters_count); ?>
                                        of <?php echo $total_chapters_count; ?> chapters (Page <?php echo $current_page; ?> of <?php echo $total_pages; ?>)
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>

                    <?php else : ?>
                        <div class="no-chapters p-4 text-center">
                            <i class="bi bi-journal-x display-4 d-block mb-3 text-muted"></i>
                            No chapters available yet.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* General Styles */
body {
    background-color: #f8f9fa;
}

.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
}

/* Novel Cover */
.novel-cover img {
    width: 100%;
    height: auto;
}

/* Rating Section */
.rating-section {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.25rem;
}

.stars .fa-star {
    color: #ffc107;
    margin-right: 2px;
}

.stars-input .fa-star {
    cursor: pointer;
    color: #ffc107;
    margin-right: 2px;
}

.rating-value {
    margin-left: 0.5rem;
    font-weight: 500;
}

.rating-count {
    color: #6c757d;
    margin-left: 0.5rem;
}

/* Novel Info */
.novel-info .table td {
    padding: 0.5rem;
    border-color: #eee;
    font-size: 0.9rem;
}

.novel-info td:first-child {
    font-weight: 500;
    width: 45%;
    color: #495057;
}

.novel-info td:first-child i {
    margin-right: 0.5rem;
    color: #6c757d;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5em 0.8em;
}

/* Chapters Section */
.chapter-item {
    border-bottom: 1px solid #eee;
}

.chapter-item:last-child {
    border-bottom: none;
}

.chapter-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #212529;
    text-decoration: none;
    transition: background-color 0.2s;
}

.chapter-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.chapter-date {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Synopsis */
.novel-synopsis {
    font-size: 0.95rem;
    line-height: 1.6;
}

.read-more {
    text-decoration: none;
    font-weight: 500;
}

/* Chapter Pagination */
.chapter-pagination {
    background-color: #f8f9fa;
}

.chapter-pagination .pagination {
    margin-bottom: 0;
}

.chapter-pagination .page-link {
    color: #0d6efd;
    border-color: #dee2e6;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.chapter-pagination .page-link:hover {
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.chapter-pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

.chapter-pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

.pagination-info {
    font-size: 0.8rem;
}

/* Responsive */
@media (max-width: 768px) {
    .chapter-link {
        flex-direction: column;
        align-items: flex-start;
    }

    .chapter-date {
        font-size: 0.8rem;
        margin-top: 0.25rem;
    }

    .chapter-pagination .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .chapter-pagination .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        margin: 0.1rem;
    }

    .pagination-info {
        font-size: 0.75rem;
        margin-top: 0.5rem;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Read More functionality
    $('.read-more').click(function(e) {
        e.preventDefault();
        $('.synopsis-short').hide();
        $('.synopsis-full').fadeIn();
    });

    // Chapter sorting with pagination
    $('#chapter-sort').change(function() {
        var order = $(this).val();
        var currentUrl = new URL(window.location.href);

        // Update sort parameter
        currentUrl.searchParams.set('sort', order);

        // Reset to first page when sorting changes
        currentUrl.searchParams.set('chapter_page', '1');

        // Redirect to new URL
        window.location.href = currentUrl.toString();
    });

    // Bookmark functionality
    $('.toggle-bookmark').on('click', function(e) {
        e.preventDefault();
        var button = $(this);
        var icon = button.find('i');
        var text = button.find('.bookmark-text');
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'toggle_bookmark',
                novel_id: button.data('novel-id'),
                security: button.data('nonce')
            },
            beforeSend: function() {
                button.prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.action === 'added') {
                        icon.removeClass('far').addClass('fas');
                        text.text('Bookmarked');
                    } else {
                        icon.removeClass('fas').addClass('far');
                        text.text('Add Bookmark');
                    }
                }
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Rating functionality
    $('.stars-input .fa-star').hover(
        function() {
            var rating = $(this).data('rating');
            for (var i = 1; i <= 5; i++) {
                if (i <= rating) {
                    $('.stars-input .fa-star:nth-child(' + i + ')').removeClass('far').addClass('fas');
                } else {
                    $('.stars-input .fa-star:nth-child(' + i + ')').removeClass('fas').addClass('far');
                }
            }
        },
        function() {
            $('.stars-input .fa-star').removeClass('fas').addClass('far');
        }
    );

    $('.stars-input .fa-star').click(function() {
        var rating = $(this).data('rating');
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'save_novel_rating',
                post_id: <?php echo get_the_ID(); ?>,
                rating: rating
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data);
                }
            }
        });
    });
});
</script>

<?php get_footer(); ?>
